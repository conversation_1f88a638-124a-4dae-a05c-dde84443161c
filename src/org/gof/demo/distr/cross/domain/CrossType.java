package org.gof.demo.distr.cross.domain;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.gof.core.support.Utils;
import org.gof.core.utils.EnumUtils;

/**
 * 跨服功能类型
 * <AUTHOR>
 *
 */
public enum CrossType {
	/** 跨服竞技场 */
	cross_arena_rank(1),
	/** 跨服排位赛 */
	cross_arena_region_rank(2),
	/** 跨服乱斗 */
	cross_chaos_battle(3),
	/** 跨服战 */
	cross_war(4),
	/** 跨服活动排行榜 */
	cross_activity(5),
	/** 武道会 */
	kung_fu_race(6),
	/** 跨服黑市 */
	cross_black_market(7),
	;
	private int type;
	private CrossType(int type) {
		this.type = type;
	}
	
	public int getType() {
		return type;
	}

	private static Map<Integer,CrossType> map = EnumUtils.toMap(CrossType.values(), "type", Integer.class);
	public static CrossType valueOf(int type) {
		return map.get(type);
	}

	/**
	 * 随机一个跨服
	 * @return
	 */
	public static CrossType randomType() {
		CrossType[] values = CrossType.values();
		return values[Utils.random(values.length)];
	}
}
