package org.gof.demo.worldsrv.mail;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pwrd.op.LogOp;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectService;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.ConfMail;
import org.gof.demo.worldsrv.entity.FillMail;
import org.gof.demo.worldsrv.entity.Mail;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgMail;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.LanguageTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.*;


public class MailManager extends ManagerBase {

	public static final long SYS_SENDER = 1;      //系统发送
	public static final int MAIL_SN_COMMON = 99991;// 普通邮件sn模板（发送活动奖励、礼包邮件时使用的sn模板）
	public static final int MAIL_SN_ATTACHMENT = 99992;// 附件邮件sn模板（发送活动奖励、礼包邮件时使用的sn模板）
	
	public static final long MAIL_EXPIRE_WITH_ATTCHMENT = 5*24*3600*1000;   //带有附件奖励的邮件有效期
	public static final long MAIL_EXPIRE_WITHOUT_ATTCHMENT = 3*24*3600*1000;   //无附件奖励的邮件有效期

	public static final int MAIL_TYPE1 = 1;// 系统邮件
	public static final int MAIL_TYPE2 = 2;// 全服邮件
	public static final int MAIL_TYPE3 = 3;// 后台邮件

	public static final int MAIL_TYPE_CREATE = 1;// 邮件创建
	public static final int MAIL_TYPE_READ = 2;// 邮件阅读
	public static final int MAIL_TYPE_DRAW = 3;// 邮件领取


	// 目前看参数不多，jsonObject够用了
	public static final String MAIL_PARAM_1 = "1";// 邮件参数1
	public static final String MAIL_PARAM_2 = "2";// 邮件参数2
	public static final String MAIL_PARAM_3 = "3";// 邮件参数3
	public static final String MAIL_PARAM_4 = "4";// 邮件参数4
	public static final String MAIL_PARAM_5 = "5";// 邮件参数5
	public static final String MAIL_PARAM_6 = "6";// 邮件参数6
	public static final String MAIL_PARAM_7 = "7";// 邮件参数7
	public static final String MAIL_PARAM_8 = "8";// 邮件参数8
	public static final String MAIL_PARAM_9 = "9";// 邮件参数9
	public static final String MAIL_PARAM_10 = "10";// 邮件参数10

	public static final int MAIL_PARAM_MAX = 10;// 邮件最大

	public static final String MAIL_K_0 = "0";// int/long值
	public static final String MAIL_K_1 = "1";// 多语言表sn
	public static final String MAIL_K_2 = "2";// 道具表sn
	public static final String MAIL_K_3 = "3";// String值
	public static final String MAIL_K_4 = "4";// int/long值
	public static final String MAIL_K_5 = "5";// 时间戳（秒）
	public static final String MAIL_K_6 = "6";// ？
	public static final String MAIL_K_7 = "7";// K = 7，V = CrossPvpGrade表sn

	public static final String MAIL_K_8 = "8";// 活动表sn

//	public static final String MAIL_K_9 = "9";// 活动期数表sn,弃用
	public static final String MAIL_K_10 = "10";// 坐骑表sn
	public static final String MAIL_K_11 = "11";// 乱斗段位

	public static final String ONLY_OLD_SERVER = "onlyOldServer";

	/**
	 * 获取实例
	 * @return
	 */
	public static MailManager inst() {
		return inst(MailManager.class);
	}
	
	/**
	 * 在线接收邮件后续操作
	 * @param humanObj
	 */
	public void addNew(HumanObject humanObj, Mail mail) {
		if(!humanObj.operation.mailMap.containsKey(mail.getId())){
			humanObj.operation.mailMap.put(mail.getId(), mail);
		}
		if(mail.getRtime() <= Port.getTime()){
			MsgMail.mail_new_s2c.Builder msg = MsgMail.mail_new_s2c.newBuilder();
			msg.setMailInfo(MailManager.inst().to_p_mail(humanObj, mail));
			humanObj.sendMsg(msg);
		}
	}

	/**
	 * 登录成功
	 * @param param
	 */
	@Listener(EventKey.HUMAN_LOGIN_FINISH)
	public void onHumanLoginFinish(Param param) {
		HumanObject humanObj = param.get("humanObj");

		FillMailServiceProxy prx = FillMailServiceProxy.newInstance();
		prx.loginCheck(humanObj.id, humanObj.getHuman().getServerId(), humanObj.operation.fillMailIdList);
		prx.listenResult(this::_result_login_fillMail, "humanObj", humanObj);
	}
	
	public void _result_login_fillMail(Param results, Param context){
		HumanObject humanObj = context.get("humanObj");
		List<FillMail> mailList = results.get("mailList");
		if(mailList == null){
			return;
		}
		List<Long> removeIdList = results.get("removeIdList");
		boolean isUpdate = false;
		if(removeIdList != null && !removeIdList.isEmpty()){
			humanObj.operation.fillMailIdList.removeAll(removeIdList);
			isUpdate = true;
		}
		List<Long> addIdList = results.get("addIdList");
		if(addIdList != null && !addIdList.isEmpty()){
			isUpdate = true;
		}
		// 先记录再发奖励
        for (FillMail mail : mailList) {
			if(humanObj.operation.fillMailIdList.contains(mail.getId())){
				continue;
			}
			isUpdate = true;
			humanObj.operation.fillMailIdList.add(mail.getId());
            if ("".equals(mail.getItemSn()) && "".equals(mail.getItemNum())) {
                MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, MailManager.MAIL_SN_COMMON, mail.getTitle(), mail.getContent(), null, null);
            } else {
				JSONObject jo = Utils.toJSONObject(mail.getParam());
				if(jo.containsKey("crossWarMailSn")){
                    // 玩家能否接收跨服战奖励的全服邮件
                    if(!canReceivedFillMail(humanObj.id, mail)){
                        return;
                    }
					if(jo.containsKey("funcSn")){
						int funcSn = jo.getIntValue("funcSn");
						if(!humanObj.isModUnlock(funcSn)){
							Log.temp.info("玩家{}未解锁功能{}", humanObj.id, funcSn);
							humanObj.operation.fillMailIdList.remove(mail.getId());
							continue;
						}
					}
					Param params = new Param();
					params.put(Mail.K.backstage, true);
					if(jo.containsKey("rank")){
						int rank = jo.getIntValue("rank");
						params.put("rank", rank);
					}
					int mailSn = MailManager.MAIL_SN_ATTACHMENT;
					if(jo.containsKey("crossWarMailSn")){
						mailSn = jo.getIntValue("crossWarMailSn");
					}
					int[] sns = Util.strToIntArray(mail.getItemSn());
					int[] nums = Util.strToIntArray(mail.getItemNum());
					MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, mailSn, mail.getTitle(), mail.getContent(), sns, nums, params);
				} else {
					int[] sns = Util.strToIntArray(mail.getItemSn());
					int[] nums = Util.strToIntArray(mail.getItemNum());
					MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, MailManager.MAIL_SN_ATTACHMENT, mail.getTitle(), mail.getContent(), sns, nums);
				}
            }
        }
		if(isUpdate){
			String redisKey = HumanManager.getFillMailReceivedKey(humanObj.id);
			List<Long> fillMailIdList = humanObj.operation.fillMailIdList;
			RedisTools.set(EntityManager.redisClient, redisKey, Utils.listToString(fillMailIdList));
		}
	}

    /**
     * 玩家能否接收全服邮件
     * @param humanId
     * @param fillMail
     * @return
     */
    static public boolean canReceivedFillMail(long humanId, FillMail fillMail) {
        // onlyOldServer表示全服邮件只发给合服前所在服务器的玩家
        JSONObject jo = Utils.toJSONObject(fillMail.getParam());
        int serverId = jo.containsKey(MailManager.ONLY_OLD_SERVER) ? Util.getServerIdOldByHumanId(humanId) : Util.getServerIdByHumanId(humanId);
        if(serverId == 0 || serverId != fillMail.getServerId()){
            Log.temp.info("===不是这个服的玩家不处理， humanId={}, serverId={}, item=[{},{}] param={}",
                    humanId, serverId, fillMail.getItemSn(), fillMail.getItemNum(), fillMail.getParam());
            return false;
        }
        return true;
    }

	/**
	 * 发送邮件给玩家
	 * <AUTHOR>
	 * @Date 2023/7/31
	 * @Param 
	 */
	public void sendMail(long receiverId, long senderId, int mailSn, String title, String content, int[] itemId, int[] num) {
		sendMail(receiverId, senderId, mailSn, title, content, itemId, num, null);
	}

	public void sendMail(long receiverId, long senderId, int mailSn, String title, String content, int[] itemId, int[] num, Param params, Object... contentParam) {
		String itemJSON = "";
		if (itemId != null && num != null) {
			Map<Integer, Integer> itemSnNumMap = new HashMap<>();
			for (int i = 0; i < itemId.length; i++) {
				int key = itemId[i];
				int value = num[i];
				if(key == 0 || value == 0){
					continue;
				}
				if(itemSnNumMap.containsKey(key)){
					itemSnNumMap.put(key, itemSnNumMap.get(key) + value);
					continue;
				}
				itemSnNumMap.put(key, value);
			}
			if(!itemSnNumMap.isEmpty()){
				itemJSON = Utils.mapIntIntToJSON(itemSnNumMap);
			}
		}
		sendMail(receiverId, senderId, mailSn, title, content, itemJSON, params, contentParam);
	}

	/**
	 * 发送邮件给玩家
	 * @param receiverId
	 * @param senderId
	 * @param mailSn
	 * @param title
	 * @param content
	 * @param itemId
	 * @param num
	 */
	public void sendMail(long receiverId, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam) {
		if (Utils.isNullOrEmptyJSONString(itemJSON)) {
			ConfMail confMail = ConfMail.get(mailSn);
			if (confMail != null && confMail.reward != null) {
				Map<Integer, Integer> itemSnNumMap = new HashMap<>();
				for (int i = 0; i < confMail.reward.length; i++) {
					int itemSn = confMail.reward[i][0];
					int itemNum = itemSnNumMap.getOrDefault(itemSn, 0) + confMail.reward[i][1];
					itemSnNumMap.put(itemSn, itemNum);
				}
				itemJSON = Utils.mapIntIntToJSON(itemSnNumMap);
			}
		}
		// 简要数据方便查询
		Log.temp.info("===发送邮件给玩家,mailSn={}, 玩家humanId={}, itemJSON={}", mailSn, receiverId, itemJSON);

//		Log.temp.info("发送邮件给玩家：receiverId={}, senderId={}, mailSn={}, title={}, content={}, itemJSON={}, params={}, contentParam={}", receiverId, senderId, mailSn, title, content, itemJSON, params, contentParam);

		//持久化邮件
		Mail mail = new Mail();
		mail.setId(Port.applyId());
		mail.setTitle(title);
		mail.setContent(content);
		mail.setRtime(Port.getTime());
		mail.setRead(false);
		mail.setHumanId(receiverId);
		mail.setSender(senderId);
		mail.setMailSn(mailSn);
		mail.setItem(itemJSON);
        boolean fillMail = false;
		if (params != null) {
			// 是否后台邮件
			if (params.containsKey(Mail.K.backstage)) {
				mail.setBackstage(params.getBoolean(Mail.K.backstage));
			}
			if (params.containsKey(Mail.K.rtime)) {
				mail.setRtime(params.getLong(Mail.K.rtime));
			}
            if (params.containsKey("FillMail")) {
                fillMail = params.getBoolean("FillMail");
            }
			if(params.containsKey("rank")){
				JSONObject jo = new JSONObject();
				JSONObject joTemp = new JSONObject();
				joTemp.put(MailManager.MAIL_K_4, params.getInt("rank"));
				jo.put(MailManager.MAIL_PARAM_1, joTemp);
				mail.setContent(jo.toJSONString());
			}
		}

		mail.persist();

		int mailType = MAIL_TYPE1; // 0系统邮件，1 全服邮件， 2后台邮件
		if (mail.isBackstage() && fillMail) {
			mailType = MAIL_TYPE2;
		} else if(mail.isBackstage()){
			mailType = MAIL_TYPE3;
		}

		LogOp.log("mail", MAIL_TYPE_CREATE , mail.getId(), mail.getHumanId(), Port.getTime(),Utils.formatTime(Port.getTime(), "yyyy-MM-dd")
				, mail.getMailSn(), mailType, mail.getItem());

		if (S.isBridge) {
			return;
		}
		if (mail.getRtime() > Port.getTime()) {
			return;
		}
		//通知给玩家
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.getInfo(receiverId);
		prx.listenResult(this::_result_remindHumanTo, "mail", mail, "humanId", receiverId);
	}

	private void _result_remindHumanTo(Param results, Param context){
		Mail mail = Utils.getParamValue(context, "mail", null);
		HumanGlobalInfo info = results.get();
		if(info==null){
			return;
		}
		if(mail == null){
			return;
		}
		HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		humanPrx.mailAccept(mail);

	}

	/**
	 * 发送邮件给工会
	 */
	public void sendMailToGuild(long receiverId, boolean skipLeader, long senderId,  int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam) {
		GuildServiceProxy prx = GuildServiceProxy.newInstance();
		prx.sendMailToGuild(receiverId, skipLeader, senderId, mailSn, title, content, itemJSON, params, contentParam);
	}

	/**
	 * 发送邮件工会族长
	 */
	public void sendMailToGuildLeader(long receiverId, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam) {
		GuildServiceProxy prx = GuildServiceProxy.newInstance();
		prx.sendMailToGuildLeader(receiverId, senderId, mailSn, title, content, itemJSON, params, contentParam);
	}

	//=================================================TODO ============================================================
	public void _msg_mail_list_c2s(HumanObject humanObj, long mailId) {
		MsgMail.mail_list_s2c.Builder msg = MsgMail.mail_list_s2c.newBuilder();
		for(Mail mail : humanObj.operation.mailMap.values()){
			if (mail.getRtime() > Port.getTime()) {
				continue;
			}
			msg.addMailList(to_p_mail(humanObj, mail));
		}
		humanObj.sendMsg(msg);
	}

	public Define.p_mail.Builder to_p_mail(HumanObject humanObj, Mail mail){
		Define.p_mail.Builder dInfo = Define.p_mail.newBuilder();
		dInfo.setId(mail.getId());
		dInfo.setCfgId(mail.getMailSn());
		dInfo.setSendId(mail.getSender());

		Define.p_lang_info.Builder dlangName = Define.p_lang_info.newBuilder();
		dlangName.setId(0);
		Define.p_key_value_name.Builder dName = Define.p_key_value_name.newBuilder();
		dName.setK(0);
		dName.setV(0);
		dName.setName("");
		dlangName.addArgList(dName);
		dInfo.setSendName(dlangName);

		Define.p_lang_info.Builder dTitle2 = Define.p_lang_info.newBuilder();
		dTitle2.setId(0);
		dInfo.setTitle(dTitle2);

		Define.p_lang_info.Builder dlangTitle = Define.p_lang_info.newBuilder();
		dlangTitle.setId(0);
		Define.p_key_value_name.Builder dTitle = Define.p_key_value_name.newBuilder();
		dTitle.setK(0);
		dTitle.setV(0);
		dTitle.setName("");
		dlangTitle.addArgList(dTitle);
		dInfo.setSendName(dlangTitle);

		Define.p_lang_info.Builder dlangContent = Define.p_lang_info.newBuilder();
		dlangContent.setId(0);
		if(!Utils.isEmptyJSONString(mail.getContent()) && !mail.getContent().isEmpty()){
			JSONObject jo = Utils.toJSONObject(mail.getContent());
			for(int i = 1; i <= MAIL_PARAM_MAX; i++){
				String key = String.valueOf(i);
				if(!jo.containsKey(key)){
					break;
				} else {
					Object obj = jo.get(key);
					if(obj instanceof JSONObject){
						JSONObject mailKeyJo = jo.getJSONObject(key);
						dlangContent.addArgList(to_p_key_value_name(mailKeyJo));
					}
				}
			}
		}
		dInfo.setContent(dlangContent);

		dInfo.setSendTime((int)(mail.getRtime() / Time.SEC));
		dInfo.setExpTime((int)(mail.getEtime() / Time.SEC));
		dInfo.setIsRead(mail.isRead() ? 1 : 0);

		Map<Integer, Integer> snNumMap = Utils.jsonToMapIntInt(mail.getItem());
		for (Map.Entry<Integer, Integer> entry : snNumMap.entrySet()) {
			Define.p_reward.Builder dReward = Define.p_reward.newBuilder();
			dReward.setGtid(entry.getKey());
			if(entry.getKey() == TokenItemType.Money999){
				long num = MoneyManager.inst().getRegionalMoney999(humanObj.getRegional(), entry.getValue());
				dReward.setNum(num);
			}else {
				dReward.setNum(entry.getValue());
			}
			dInfo.addGoodsList(dReward);
		}

		// 有附件并且已领取的情况下，IsAttach为0
		if(snNumMap.size() != 0){
			dInfo.setIsAttach(mail.isReceive() ? 0 : 1);
		} else {
			dInfo.setIsAttach(0);
		}

		// 针对后台邮件，内容就纯文本，读不了表
		LanguageTypeKey languageType = LanguageTypeKey.getEnumByType(humanObj.languageType);
		if(languageType == null){
			languageType = LanguageTypeKey.ja;
		}
        String language = languageType.getName();
//        Log.temp.info("languageType={} language={}", languageType.getType(), language);
		// 邮件标题和内容解析多语言，如果没有本语言则显示日语，如果日语也没有则显示原内容
		JSONObject joContent = Utils.toJSONObject(mail.getContent());
		String contentStr = joContent.getString(language);
		if(contentStr == null){
			contentStr = joContent.getString(LanguageTypeKey.ja.getName());
			if(contentStr == null){
				contentStr = mail.getContent();
			}
		}
		dInfo.setContentStr(contentStr);
		JSONObject joTitle = Utils.toJSONObject(mail.getTitle());
		String titleStr = joTitle.getString(language);
		if(titleStr == null){
			titleStr = joTitle.getString(LanguageTypeKey.ja.getName());
			if(titleStr == null){
				titleStr = mail.getTitle();
			}
		}
		dInfo.setTitleStr(titleStr);

		return dInfo;
	}

	private Define.p_key_value_name to_p_key_value_name(JSONObject mailKeyJo){
		Define.p_key_value_name.Builder dContent = Define.p_key_value_name.newBuilder();
		// 先默认值，避免报错
		dContent.setK(Utils.intValue(MAIL_K_0));
		dContent.setV(0);
		dContent.setName("");
		for(String key : mailKeyJo.keySet()){
			dContent.setK(Utils.intValue(key));
			switch (key){
				case MAIL_K_0:
				case MAIL_K_1:
				case MAIL_K_2:
				case MAIL_K_10:
				case MAIL_K_11:
					dContent.setV(mailKeyJo.getIntValue(key));
					break;
				case MAIL_K_3:
					dContent.setName(mailKeyJo.getString(key));
					break;
				case MAIL_K_4:
					dContent.setV(mailKeyJo.getLongValue(key));
					break;
				case MAIL_K_5:
					dContent.setV(mailKeyJo.getIntValue(key));
					break;
				case MAIL_K_6:
					break;
				case MAIL_K_7:
					dContent.setV(mailKeyJo.getIntValue(key));
					break;
				case MAIL_K_8:
					dContent.setV(mailKeyJo.getIntValue(key));
					break;

				default:
					Log.temp.error("===类型错误，先记录obj={}", mailKeyJo);
					break;
			}
		}
		return dContent.build();
	}


	public void _msg_mail_read_c2s(HumanObject humanObj, long mailId) {
		Mail mail = humanObj.operation.mailMap.get(mailId);
		if(mail == null){
			Log.temp.error("邮件不存在，humanName={} mid={}",humanObj.name, mailId);
			return;
		}
		mail.setRead(true);
		MsgMail.mail_read_s2c.Builder msg = MsgMail.mail_read_s2c.newBuilder();
		msg.setMailId(mailId);
		humanObj.sendMsg(msg);
	}

	/**
	 * 领取邮件附件
	 * <AUTHOR>
	 * @Date 2024/3/15
	 * @Param
	 */
	public void _msg_mail_claim_c2s(HumanObject humanObj, long mailId, int type) {
		List<Long> mailIdList = new ArrayList<>();
		boolean isOnekey = mailId <= 0;
		List<Define.p_reward> rewardList = new ArrayList<>(0);
		for (Mail mail : humanObj.operation.mailMap.values()) {
			if (isOnekey) {
				if (mail.isReceive()) {
					continue;
				}
				mail.setReceive(true);
				mail.setRead(true);
				Map<Integer, Integer> snNumMap = Utils.jsonToMapIntInt(mail.getItem());
				rewardList.addAll(ProduceManager.inst().rewardProduceToMsg(humanObj, snNumMap, MoneyItemLogKey.获取邮件附件));
				mailIdList.add(mail.getId());
				continue;
			}
			if (mail.getId() == mailId) {
				if (mail.isReceive()) {
					return;
				}
				mail.setReceive(true);
				mail.setRead(true);
				Map<Integer, Integer> snNumMap = Utils.jsonToMapIntInt(mail.getItem());
				rewardList.addAll(ProduceManager.inst().rewardProduceToMsg(humanObj, snNumMap, MoneyItemLogKey.获取邮件附件));
				mailIdList.add(mail.getId());
				break;
			}
		}

		MsgMail.mail_claim_s2c.Builder msg = MsgMail.mail_claim_s2c.newBuilder();
		msg.addAllClaimList(mailIdList);
		humanObj.sendMsg(msg);

		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardList);
	}


	/**
	 * 删除邮件
	 * <AUTHOR>
	 * @Date 2024/3/15
	 * @Param
	 */
	public void _msg_mail_delete_c2s(HumanObject humanObj, long mailId, int type) {
		List<Long> deleteIdList = new ArrayList<>();
		boolean isOnekey = mailId <= 0;
		List<Mail> removeMailList = new ArrayList<>();
		for (Mail mail : humanObj.operation.mailMap.values()) {
			if(!mail.isRead()){
				continue;
			}
			if(!mail.isReceive() && !mail.getItem().isEmpty() && !Utils.isEmptyJSONString(mail.getItem())){
				continue;
			}
			if(isOnekey){
				if (mail.isNotice()) {
					Log.game.error("公告类邮件不允许主动删除，humanName={} mid={}",humanObj.name, mailId);
					continue;
				}
				removeMailList.add(mail);
				continue;
			}
			if (mail.getId() == mailId) {
				removeMailList.add(mail);
				break;
			}
		}
		for(Mail mail : removeMailList){
			deleteIdList.add(mail.getId());
			humanObj.operation.mailMap.remove(mail.getId());
			mail.remove();
		}

		MsgMail.mail_delete_s2c.Builder msg = MsgMail.mail_delete_s2c.newBuilder();
		msg.setType(type);
		msg.addAllDeleteList(deleteIdList);
		humanObj.sendMsg(msg);
	}

	public void _msg_mail_expired_reward_c2s(HumanObject humanObj) {

	}

	/**
	 * 清理过期邮件
	 * @param humanObj
	 * @return
	 */
	private List<Long> clearExpire(HumanObject humanObj){
		List<Mail> list = new ArrayList<>(humanObj.operation.mailMap.values());
		List<Long> mailIds = new ArrayList<>();

		Iterator<Mail> iter = list.iterator();
		while(iter.hasNext()){
			Mail mail = iter.next();
			if(Port.getTime() > mail.getEtime()){
				mailIds.add(mail.getId());
				iter.remove();
				mail.remove();
				continue;
			}
			if (mail.isNotice()) {
				// 公告类邮件
				continue;
			}
			if("{}".equals(mail.getItem()) && mail.isRead()){
				mailIds.add(mail.getId());
				iter.remove();
				mail.remove();
				continue;
			}
			if(!"{}".equals(mail.getItem()) && mail.isReceive()){
				mailIds.add(mail.getId());
				iter.remove();
				mail.remove();
			}
		}
		return mailIds;
	}


	public void createFillMail(int serverId, int mailSn, long startTime, long endTime, int rank, int levelMin, int levelMax,int[] itemSnArr, int[] itemNumArr){
		long timeNow = Port.getTime();
		FillMail fillMail = new FillMail();
		fillMail.setId(Port.applyId());
		fillMail.setStartTime(startTime);
		fillMail.setEndTime(endTime);
		fillMail.setLevelMin(levelMin);
		fillMail.setLevelMax(levelMax);
		fillMail.setRegisteredBefore(Port.getTime());
		fillMail.setRegisteredAfter(0);
		JSONObject jo = new JSONObject();
		jo.put("crossWarMailSn", mailSn);
		jo.put("funcSn", 70);
		jo.put("rank", rank);
        jo.put(ONLY_OLD_SERVER, 1);
		fillMail.setParam(jo.toJSONString());
		fillMail.setSendTime(timeNow);
		fillMail.setServerId(serverId);
		fillMail.setItemSn(Utils.arrayIntToStr(itemSnArr));
		fillMail.setItemNum(Utils.arrayIntToStr(itemNumArr));
		fillMail.persist();
		fillMail.setSysSendTime(Port.getTime());

		FillMailServiceProxy proxy = FillMailServiceProxy.newInstance();
		proxy.addFillMail(fillMail);

	}
}
