package org.gof.demo.worldsrv.guild;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.Command;
import io.vertx.redis.client.Request;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.redis.*;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.core.support.S;
import org.gof.demo.battlesrv.battle.BattleLib;
import org.gof.demo.battlesrv.battle.enumVo.RunState;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.support.DataPreloader;
import org.gof.demo.support.IPersistObj;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.allot.AllotServerServiceProxy;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueRecord;
import org.gof.demo.worldsrv.callback.MemberCallback;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.league.GuildLeagueServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpServiceProxy;
import org.gof.demo.worldsrv.guild.league.LeagueVO;
import org.gof.demo.worldsrv.guild.league.match.LeagueMatchGroup;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.InformParam;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.gof.demo.worldsrv.equip.EquipInfo.face;
import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

/**
 * @program: game2
 * @description: 公会服务
 * @author: Mr.wugz
 * @create: 2024-04-02 14:46
 **/
@DistrClass(
        servId = D.SERV_GUILD,
        importClass = { Param.class, List.class, HumanData.class, RankInfo.class, LeagueVO.class, Map.class }

)
public class GuildService extends GameServiceBase {

    // 公会信息
    public Map<Long, GuildData> guildMap = new ConcurrentHashMap<>();
    // 服，服内公会
    public Map<Integer, List<Long>> serverIdGuildIdListMap = new HashMap<>();
    private Set<String> nameList = new HashSet<>();

    private Set<Long> enrollGuildIdSet = new HashSet<>();

    // 玩家id, 工會id
    public Map<Long, Long> humanIdGuildIdMap = new HashMap<>();

    // 玩家id, 申請的公會id
    public Map<Long, List<Long>> humanIdApplyGuildIdMap = new HashMap<>();

    private long rankUpTime = 0;

    private Map<Long, HumanData> rankHumanDataMap = new ConcurrentHashMap<>();

    private boolean guildGveOpen = false;
    private boolean guildGveReady = false;

    // 本服内显示
    public Map<Integer, List<GuildRankInfo>> serverIdGuildRankListMap = new HashMap<>();

    public Map<Integer, List<GuildRankInfo>> serverIdGuildCombatRankListMap = new HashMap<>();

    public Map<Long, GuildRankInfo> guildIdRankInfoMap = new HashMap<>();
    private TickTimer ttGveReady = new TickTimer();
    private TickTimer ttGveOpen = new TickTimer();
    private TickTimer ttGveEnd = new TickTimer();
    private Set<Long> gveTodayRewardHumanIdSet = new HashSet<>();// 今日gve领取报名排行奖励的玩家id集合

    public static final int turnSum = 5;
    private int turnNow = 0;
    private TickTimer ttBoxOpen = new TickTimer();
    private TickTimer ttBoxTurn = new TickTimer();

    private TickTimer ttAutoJoin = new TickTimer();

    private TickTimer testTimer5sec = new TickTimer(60 * Time.SEC);

    private List<Integer> serverIdListNow = new ArrayList<>();

    private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理

    private TickTimer ttSec = new TickTimer(Time.SEC);

    private TickTimer ttSecTwoCehck = new TickTimer(2 * Time.SEC);

    public LinkedList<LeagueVO> guildVoList = new LinkedList<>();// 乱斗公会奖励

    public LinkedList<LeagueVO> sasonResetList = new LinkedList<>();// 乱斗公会重置邮件
    private LinkedList<Long> checkSyncHumanIdList = new LinkedList<>();// 检测同步玩家id列表
    private TickTimer ttSeasonResetCehck = new TickTimer(5 * Time.SEC);
	
	 // 趣味竞答题库
    private final List<ConfQuiz> questionList = new ArrayList<>();
    // 趣味竞答结算计时器
    private final TickTimer ttQuestionReward = new TickTimer();
    // 趣味竞答获取公会总榜计时器
    private final TickTimer ttQuestionGetRank = new TickTimer();
    // 趣味竞答公会总榜列表长度
    public static final int QUESTION_GUILD_RANK_LIST_NUM = 5;
    // 趣味竞答公会总榜列表
    private List<GuildQuestionRankInfo> guildQuestionRankInfoList = new ArrayList<>();
	
    /**
     * 构造函数
     *
     * @param port
     */
    public GuildService(GamePort port) {
        super(port);
    }

    private void updateLeagueStep(){
//        if (step == 0){
//            step = GuildLeagueUtils.nowStep();
//        }
//        int oldStep = step;
//        if (oldStep == ParamKey.guild_gvg_step_1){
//            if (GuildLeagueUtils.getStepEndTime(step) - 1000 <= Port.getTime()){
//                Log.temp.error("=================执行自动报名");
                autoGuildLeague();
//            }
//        }
    }

    private void updateGuildAll(){
        for(GuildData guildData : guildMap.values()){
            guildData.getGuild().update();
        }
    }

    @Override
    protected void init() {
        Log.temp.info("=================init GuildService");

//        initTabale();
        GuildLeagueUtils.initStepTime();
        initGveTime();
        checkAutoJoin();
        checkServerId();
        updateLeagueStep();
        addGuildAutoDisbandSchedule();// 添加家族自动解散的定时任务

        if(!Config.DATA_DEBUG || S.isRedis){// TODO 先不加载。
            return;
        }
        if(Utils.isDebugMode()){
            return;
        }
        String whereSql = Utils.createStr(" where `{}` = {}", Guild.K.gameServerId, C.GAME_SERVER_ID);

        PersistGuild persistGuild = new PersistGuild();
        DataPreloader.PreloadByPage(persistGuild, persistGuild.getTable(), persistGuild.getPageNum(), whereSql);

        String sqlGuildIdStr = guildMap.keySet().stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        if (sqlGuildIdStr.isEmpty()) {
            Log.temp.error("没有找到公会ID，无法加载数据");
            return;
        }

        String whereSqlMember = Utils.createStr(" where `{}` in ({})", GuildMember.K.guildId, sqlGuildIdStr);
        PersistGuildMember persistGuildMember = new PersistGuildMember();
        DataPreloader.PreloadByPage(persistGuildMember, persistGuildMember.getTable(), persistGuildMember.getPageNum(), whereSqlMember);

        String whereSqlGuildApply = Utils.createStr(" where `{}` in ({})", GuildApply.K.guildId, sqlGuildIdStr);
        PersistGuildApply persistApply = new PersistGuildApply();
        DataPreloader.PreloadByPage(persistApply, persistApply.getTable(), persistApply.getPageNum(), whereSqlGuildApply);

        String whereSqlLog = Utils.createStr(" where `{}` in ({})", GuildLog.K.guildId, sqlGuildIdStr);
        PersistGuildLog persistLog = new PersistGuildLog();
        DataPreloader.PreloadByPage(persistLog, persistLog.getTable(), persistLog.getPageNum(), whereSqlLog);

        String whereSqlHelp = Utils.createStr(" where `{}` in ({})", GuildHelp.K.guildId, sqlGuildIdStr);
        PersistGuildHelp persistGuildHelp = new PersistGuildHelp();
        DataPreloader.PreloadByPage(persistGuildHelp, persistGuildHelp.getTable(), persistGuildHelp.getPageNum(), whereSqlHelp);

        initCheckRank();
    }

    /**
     * 家族自动解散
     */
    private void addGuildAutoDisbandSchedule() {
        if (ConfGlobal.get(ConfGlobalKey.公会自动解散.SN) != null) {
            scheduleCron(new ScheduleTask() {
                @Override
                public void execute() {
                    List<GuildData> guildList = new ArrayList(guildMap.values());
                    for (GuildData guildData : guildList) {
                        if (guildData.checkAutoDisband()) {
                            removeGuild(guildData.getId());
                        }
                    }
                }
            }, ConfGlobal.get(ConfGlobalKey.公会自动解散.SN).strValue);
        }
    }

    private void initGveTime(){
        if (guildGveOpen) {
            return;
        }
        try {
            ttGveReady.stop();
            ttGveOpen.stop();
            long timeNow = Port.getTime();
            long timeOpen = GuildManager.inst().getGveTime(true, true);
            long timeEnd = GuildManager.inst().getGveTime(false, false);
            long timeReady = timeOpen - ConfGlobal.get(ConfGlobalKey.league_gve_chapter_time_limit.SN).value * Time.SEC;

            if (timeOpen > 0) {
                long interval = timeOpen - timeNow;
                if (interval > 1000) {
                    ttGveOpen.start(interval);
                }

                long readyInterval = timeReady - timeNow;
                if (readyInterval > 1000) {
                    ttGveReady.start(readyInterval);
                }
                Log.temp.info("===gve开始时间={}，{}", timeOpen, Utils.formatTime(timeOpen, "yyyy-MM-dd HH:mm:ss"));
            }

            if (!ttGveEnd.isStarted() && timeEnd > timeNow) {
                ttGveEnd.start(timeEnd - timeNow);
                Log.temp.info("===gve结束时间={}，{}", timeEnd, Utils.formatTime(timeEnd, "yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            Log.temp.error("initGveTime error: {}", e.getMessage());
        }
    }


    private void initTabale(){
        // TODO 后续优化 不能用findBy
//        ConfTreasureHunt conf = ConfTreasureHunt.findBy(ConfTreasureHunt.K.turn, OrderBy.DESC).get(0);
//        if(conf != null){
//            turnSum = conf.turn;
//        }
//        initBox();
    }

    @DistrMethod
    public void loadServer(List<Integer> serverIdList){
        Log.temp.error("loadServer {}", serverIdList);
        Collection<Integer> diffAdd = CollectionUtils.subtract(serverIdList, serverIdListNow);
        if (diffAdd.isEmpty()) {
            return;
        }
        for (int serverId : diffAdd) {
            serverIdListNow.add(serverId);

            EntityManager.getEntityListAsync(Guild.class, serverId, (res) -> {
                if (!res.succeeded()) {
                    //加载失败
                    Log.temp.error("===加载Guild失败，serverId={}", serverId);
                    return;
                }
                List<Guild> modelRoundList = res.result();
                for (Guild model : modelRoundList) {
                    loadGuild(model);
                }
            });
        }

    }


    private void initBoxTickTimer() {
        long timeNow = Port.getTime();
        ConfGlobal confSwitch = ConfGlobal.get(ConfGlobalKey.treasure_hunt_open.SN);
        if (confSwitch != null && confSwitch.value == 1) {
            return;
        }

        ConfGlobal confBox = ConfGlobal.get(ConfGlobalKey.treasure_hunt_exist_time.SN);
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.treasure_hunt_time.SN);
        int[] intArr = Utils.parseIntArray2(confGlobal.strValue)[0];

        long timeOpen = Utils.getDayTime(timeNow, intArr[0], intArr[1], intArr[2]);
        Log.guild.error("家族宝箱开启时间：timeOpen={}", timeOpen);
        long timeEnd = timeOpen + confBox.value * Time.SEC * turnSum;
        if (timeNow >= timeEnd) {
            timeOpen += Time.DAY;
            ttBoxOpen.start(timeOpen - timeNow);
        } else if (timeNow >= timeOpen) {
            int turn = (int) ((timeNow - timeOpen) / (confBox.value * Time.SEC));
            actBoxStart(timeOpen, turn + 1);
        } else {
            ttBoxOpen.start(timeOpen - timeNow);
        }
    }

    private void initCheckRank(){
//        Log.temp.info("initCheckRank {}", guildMap.size());
        List<GuildData> rankList = new ArrayList<>(guildMap.values());
        List<Integer> serverIdList = new ArrayList<>();
        for(GuildData guildData : rankList){
            updateRank(guildData, false);
            if(!serverIdList.contains(guildData.getServerId())){
                serverIdList.add(guildData.getServerId());
            }
        }
        for(int serverId : serverIdList){
            checkRank(serverId);
            checkRankCombat(serverId, true);
        }

    }

    private class PersistGuildHelp implements IPersistObj {
        public String getTable() {
            return GuildHelp.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            GuildHelp guildHelp = new GuildHelp(r);
            loadGuildHelp(guildHelp);
        }
    }

    private void loadGuildHelp(GuildHelp guildHelp) {
        GuildData guildData = guildMap.get(guildHelp.getGuildId());
        if(guildData == null){
            Log.temp.error("loadGuildHelp guildId={} not exist", guildHelp.getGuildId());
            return;
        }
        guildData.loadGuildHelp(guildHelp);
    }

    private class PersistGuild implements IPersistObj {
        public String getTable() {
            return Guild.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            Guild guild = new Guild(r);
            loadGuild(guild);
        }
    }

    private void loadGuild(Guild guild){
//        Log.temp.info("loadGuild {}, {}", guild.getId(), guild.getName());
        long guildId = guild.getId();
        if(guild.getRemoveTime() > 0){
            return;
        }
        if(guildMap.containsKey(guildId)){
            return;
        }
        GuildData guildData = new GuildData(guild, this);

        guildMap.put(guildId, guildData);
        String guildName = guild.getName();
        if(nameList.contains(guildName)){
            // 公会重名了，强行改为旧名字+#serverId，并给免费改名1次
            String newGuildName = Utils.createStr("{}#{}", guildName, Utils.getServerIdTo(guild.getGameServerId()));
            guild.setName(newGuildName);
            guild.setFreeGuildNameNum(1);
            Log.guild.warn("===guild[{},{}]重名，改名为{}", guildId, guildName, newGuildName);
        }
        else{
            nameList.add(guildName);
        }

        List<Long> guildIdList = serverIdGuildIdListMap.computeIfAbsent(Config.SERVER_ID, k -> new ArrayList<>());
        guildIdList.add(guildId);

        // 加载成员
        List<GuildMember> modelMemberList = EntityManager.getEntityList(GuildMember.class, guildId);
        for (GuildMember member : modelMemberList) {
            member.setOnline(0);
            guildData.loadMember(member);
            humanIdGuildIdMap.put(member.getHumanId(), member.getGuildId());
        }
        guildData.updateCombat();

        checkSyncGuild(guildData);

        // 公会的很多重置牵扯到成员，所以等成员加载完再调用
        long timeNow = Port.getTime();
        if (!Utils.isSameDay(timeNow, guild.getZeroResetTime())) {
            zeroResetGuild(guildData);
        }
        updateRank(guildData, true);

        EntityManager.getEntityListAsync(GuildApply.class, guildId, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载GuildApply失败，group={}", guildId);
                return;
            }
            // 申请
            List<GuildApply> modelApplyList = res.result();
            for(GuildApply apply : modelApplyList){
                guildData.loadApply(apply);
                addHumanIdApplyGuildIdMap(apply.getHumanId(), guildId);
            }
        });


        EntityManager.getEntityListAsync(GuildLog.class, guildId, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载GuildLog失败，group={}", guildId);
                return;
            }
            List<GuildLog> modelGuildLogList = res.result();
            for(GuildLog log : modelGuildLogList) {
                guildData.loadLog(log);
            }
        });

        EntityManager.getEntityListAsync(GuildHelp.class, guildId, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载GuildHelp失败，group={}", guildId);
                return;
            }
            List<GuildHelp> modelGuildLogList = res.result();
            for(GuildHelp help : modelGuildLogList) {
                guildData.loadGuildHelp(help);
            }
        });

//        autoGuildLeague(guildData);
//        updateRank(guildData, false);
        Log.temp.info("loadGuild [{},{}] end", guildId, guild.getName());
    }

    private class PersistGuildMember implements IPersistObj {
        public String getTable() {
            return GuildMember.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            GuildMember member = new GuildMember(r);
            member.setOnline(0);
            member.update();
            if (!guildMap.containsKey(member.getGuildId())) {
                return;
            }

            GuildData guildData = guildMap.get(member.getGuildId());
            guildData.loadMember(member);
            humanIdGuildIdMap.put(member.getHumanId(), member.getGuildId());
        }
    }
    private class PersistGuildApply implements IPersistObj {
        public String getTable() {
            return GuildApply.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            GuildApply apply = new GuildApply(r);
            long guildId = apply.getGuildId();
            if (!guildMap.containsKey(guildId)) {
                return;
            }

            GuildData guildData = guildMap.get(guildId);
            guildData.loadApply(apply);
            addHumanIdApplyGuildIdMap(apply.getHumanId(), guildId);
        }
    }

    private class PersistGuildLog implements IPersistObj {
        public String getTable() {
            return GuildLog.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            GuildLog log = new GuildLog(r);
            long guildId = log.getGuildId();
            if (!guildMap.containsKey(guildId)) {
                return;
            }

            GuildData guildData = guildMap.get(guildId);
            guildData.loadLog(log);
        }
    }

    private void addHumanIdApplyGuildIdMap(long humanId, long guildId){
        List<Long> guildIdList = humanIdApplyGuildIdMap.get(humanId);
        if(guildIdList == null){
            guildIdList = new ArrayList<>();
            humanIdApplyGuildIdMap.put(humanId, guildIdList);
        }
        if(!guildIdList.contains(guildId)){
            guildIdList.add(guildId);
        }
    }

    @Override
    public void pulseOverride() {
        long nowTime = Port.getTime();
        try {
            pulseGve(nowTime);
            checkActBox(nowTime);
            if(ttSec.isPeriod(nowTime)){
                sendGuildReward();
            }
            if(ttSecTwoCehck.isPeriod(nowTime)){
                checkSyncHuman();
            }
            if(ttSeasonResetCehck.isPeriod(nowTime)){
                checkSeasonResetMail();
            }
//			pulseActQuestion(nowTime);
        } catch (Exception e) {
            Log.temp.error("pulse error {}", e.getMessage(), e);
        }
    }

    private void pulseGve(long nowTime) {
        if (ttGveReady.isPeriod(nowTime)) {
            gveReady();
            Log.temp.error("===公会Gve胖头鱼准备缓存玩家数据");
        }
        if (ttGveOpen.isPeriod(nowTime)) {
            gveOpen();
            Log.temp.error("===公会Gve胖头鱼开始");
        }
        if (ttGveEnd.isPeriod(nowTime)) {
            gveEnd();
            Log.temp.error("===公会Gve胖头鱼结束");
        }
        if (ttAutoJoin.isPeriod(nowTime)) {
            // 乱斗加入
            checkAutoJoin();
            guildAutoJoin();
        }
        if (!ttGveOpen.isStarted()) {
            initGveTime();
        }

        if (guildGveOpen) {
            // gve战斗
            guildGveBattle();
        }
    }

    private void checkServerId(){
        List<Integer> serverList = Util.getServerTagList(Config.SERVER_ID);
        if (serverList.isEmpty()) {
            Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
            return;
        }
        boolean isUp = false;
        for (Integer serverId : serverList) {
            if (!serverIdListNow.contains(serverId)) {
                serverIdListNow.add(serverId);
                List<Long> gveSignList = new ArrayList<>();
                List<Guild> guildList = EntityManager.getEntityList(Guild.class, serverId);
                for (Guild guild : guildList) {
                    loadGuild(guild);
                    GuildData data = guildMap.get(guild.getId());
                    if (data == null) {
                        continue;
                    }
                    gveSignList.addAll(data.gveSignList);
                }
                if (!gveSignList.isEmpty()) {
                    Log.guild.error("启服gve查询报名玩家，共{}人报名", gveSignList.size());
                    EntityManager.batchGetEntity(HumanBrief.class, gveSignList, (res) -> {
                        if (res.failed()) {
                            Log.guild.error("启服gve查询报名玩家错误，统一查询玩家HumanBrief失败，humanIdList={}，e={}", Utils.listToString(gveSignList), res.cause());
                            return;
                        }
                        Log.guild.error("启服gve查询报名玩家完毕");
                        List<HumanBrief> briefList = res.result();
                        for (HumanBrief brief : briefList) {
                            GuildData guildData = guildMap.get(humanIdGuildIdMap.get(brief.getId()));
                            if (guildData == null) {
                                continue;
                            }
                            byte[] briefBytes = new HumanBriefVO(brief).getBattleRoleByPlanType(HumanManager.PLAN_TYPE_GUILD_BOSS);
                            guildData.updateGveBriefCache(brief.getId(), briefBytes);
                        }
                    });
                }
                isUp = true;
            }
        }
        if (isUp) {
            initCheckRank();
            initBoxTickTimer();
//            resetActQuestion();
        }
//        Collection<Integer> diffAdd = (Collection<Integer>)CollectionUtils.subtract(serverIdListNow, serverList);
//        if (diffAdd.isEmpty()) {
//            return;
//        }
//        for(Integer serverId : diffAdd){
//            serverIdListNow.remove(serverId);
//            removeServerId(serverId);
//        }
//        Log.temp.info("===本服id={},  已经下线serverIds={}", C.GAME_SERVER_ID, diffAdd);
//        serverList.clear();
//        diffAdd.clear();

    }

    private void removeServerId(int serverId){
        List<Long> guildIdList = serverIdGuildIdListMap.get(serverId);
        if(guildIdList != null){
            for(long guildId : guildIdList){
                guildMap.remove(guildId);
            }
        }
        serverIdGuildIdListMap.remove(serverId);
    }

    private void guildGveBattle(){
        for (GuildData guildData : guildMap.values()){
            guildData.gveBattlePulse();
        }
    }


    private void guildAutoJoin(){
        boolean isBridge = GuildLeagueUtils.isBridge(Config.SERVER_ID);
        for(GuildData guildData : guildMap.values()){
            if(guildData == null){
                continue;
            }
            if(guildData.getMemberNum() < GlobalConfVal.guildFamilySignupNum){
                continue;
            }
            long guildId = guildData.getGuild().getId();
            List<Long> humanIdList = guildData.getHumanIdList();
            if(Config.DEBUG_PATTERN && !GuildLeagueUtils.isBridge(guildData.getServerId())){ // 内网本服报名
                GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
                proxy.autoJoinHumanIdList(guildId, humanIdList);
            } else if(!isBridge){ // 本服报名
                GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
                proxy.autoJoinHumanIdList(guildId, humanIdList);
                Log.temp.info("===公会{}自动加入赛季{}， 本服", guildId, humanIdList.size());
            } else{
                try {
                    CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, guildData.getServerId(), res -> {
                        try {
                            if(res.failed()){
                                Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                                return;
                            }
                            CrossPoint result = res.result();
                            GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                            proxy.autoJoinHumanIdList(guildId, humanIdList);
                            Log.temp.info("===公会{}自动加入赛季, {}， 跨服", guildId, humanIdList.size());
                        } catch (Exception e){
                            Log.temp.error("==跨服链接出问题 ", e);
                        }
                    });
                } catch (Exception e){
                    Log.temp.error("====跨服获取数据出问题", e);
                }

            }
        }
    }

    private void checkAutoJoin(){
        long timeNow = Port.getTime();
        long timeOpen = GuildManager.inst().getAutoJoinTime();
        long interval = timeOpen - timeNow;
        if(interval > 1000){
            ttAutoJoin.start(interval);
        }
    }

    /**
     * 家族boss战进入等待，通知所有报名并且在线的玩家去缓存自己的HumanData
     */
    private void gveReady() {
        guildGveReady = true;
        ttGveReady.stop();
        List<Long> signHumanIdList = new ArrayList<>();
        for (GuildData guildData : guildMap.values()) {
            signHumanIdList.addAll(guildData.gveSignList);
        }
        // 通知所有报名的人，胖头鱼要开了，去GuildService缓存数据
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.eventFire(signHumanIdList, EventKey.GUILD_GVE_CACHE_HUMANDATA, new Param());
    }

    private void gveOpen() {
        guildGveOpen = true;
        ttGveOpen.stop();
        long timeNow = Port.getTime();
        long timeEnd = GuildManager.inst().getGveTime(false, false);
        if (timeEnd > timeNow) {
            ttGveEnd.start(timeEnd - timeNow);
        }
        // 统一一次查询全部的玩家id
        Map<Long, Long> humanIdGuildIdMap = new HashMap<>();
        List<Long> queryHumanIdList = new ArrayList<>();
        for (GuildData guildData : guildMap.values()) {
            guildData.gveBattleInit();
            if (guildData.gveSignList.isEmpty()) {
                guildData.gvePhase = 0;// 状态改为0，让开打以后其他人再报名还能继续走流程
            } else {
                guildData.gvePhase = 1;// 状态全改成初始化
                List<Long> gveQueryHumanIdList = guildData.getGveQueryHumanIdList();
                queryHumanIdList.addAll(gveQueryHumanIdList);
                for (long humanId : gveQueryHumanIdList) {
                    humanIdGuildIdMap.put(humanId, guildData.getGuild().getId());
                }
            }
        }
        if (queryHumanIdList.isEmpty()) {
            Log.guild.error("===胖头鱼战斗开始，所有玩家信息都缓存在公会中，无需查询");
            for (GuildData guildData : guildMap.values()) {
                if (guildData.gveSignList.isEmpty()) {
                    guildData.gvePhase = 0;// 状态改为0，让开打以后其他人再报名还能继续走流程
                    continue;
                }
                guildData.gveBattleStart();
                guildData.sendMsg_guild_boss_start_s2c(timeNow, timeEnd);
                guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_3, 1);
            }
        } else {
            Log.guild.error("===胖头鱼战斗开始，统一查询报名但没有缓存信息的玩家，humanIdList={}", Utils.listToString(queryHumanIdList));
            EntityManager.batchGetEntity(HumanBrief.class, queryHumanIdList, (res) -> {
                if (res.failed()) {
                    Log.guild.error("===胖头鱼战斗开始，统一查询玩家HumanBrief失败，humanIdList={}，e={}", Utils.listToString(queryHumanIdList), res.cause());
                    // 继续走流程, 没有查到的玩家数据跳过
                    for (GuildData guildData : guildMap.values()) {
                        if (guildData.gveSignList.isEmpty()) {
                            guildData.gvePhase = 0;// 状态改为0，让开打以后其他人再报名还能继续走流程
                            continue;
                        }
                        guildData.gveBattleStart();
                        guildData.sendMsg_guild_boss_start_s2c(timeNow, timeEnd);
                        guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_3, 1);
                    }
                } else {
                    List<HumanBrief> briefList = res.result();
                    for (HumanBrief brief : briefList) {
                        GuildData guildData = guildMap.get(humanIdGuildIdMap.get(brief.getId()));
                        if (guildData == null) {
                            continue;
                        }
                        byte[] briefBytes = new HumanBriefVO(brief).getBattleRoleByPlanType(HumanManager.PLAN_TYPE_GUILD_BOSS);
                        guildData.updateGveBriefCache(brief.getId(), briefBytes);
                    }
                    for (GuildData guildData : guildMap.values()) {
                        if (guildData.gveSignList.isEmpty()) {
                            guildData.gvePhase = 0;// 状态改为0，让开打以后其他人再报名还能继续走流程
                            continue;
                        }
                        guildData.gveBattleStart();
                        guildData.sendMsg_guild_boss_start_s2c(timeNow, timeEnd);
                        guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_3, 1);
                    }
                }
            });
        }
    }


    @DistrMethod
    public void createGuild(long humanId, String name, int serverId){
        if (humanIdGuildIdMap.containsKey(humanId)) {
            port.returns("guildId", 0L, "result", new ReasonResult(false, ErrorTip.GuildHasJoin));
            return;
        }
        if (nameList.contains(name)) {
            port.returns("guildId", 0L, "result", new ReasonResult(false, ErrorTip.GuildNameRepeate));
            return;
        }
        Guild guild = new Guild();
        guild.setId(Port.applyId());
        guild.setLeaderId(humanId);
        guild.setName(name);
        guild.setCreateTime(Port.getTime());
        guild.setGameServerId(serverId);
        guild.setAutoJoin(GuildParamKey.joinStatusAuto);
        guild.setZeroResetTime(Port.getTime());
        guild.persist();


        loadGuild(guild);
        humanIdGuildIdMap.put(humanId, guild.getId());
        removeApply(humanId);
        port.returns("guildId", guild.getId(), "result", new ReasonResult(true));
    }

    @DistrMethod
    public void nameChange(long guildId, long humanId, String name){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        int pos = guildData.getPosition(humanId);
        if(!isPermission(pos, GuildParamKey.guildPermission_2)){
            return;
        }
        guildData.getGuild().setName(name);
        syncGuildLeagueCross(guildData, new Param(GuildLeagueRecord.K.guildName, name));
    }

    @DistrMethod
    public void rollBackCreateGuild(long guildId){
        removeGuild(guildId);
    }

    @DistrMethod
    public void createGuildMember(Param param){
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        String name = Utils.getParamValue(param, "name", "");
        long guildId = Utils.getParamValue(param, "guildId", 0L);
        String head = Utils.getParamValue(param, "head", "");
        int online = Utils.getParamValue(param, "online", 0);
        long combat = Utils.getParamValue(param, "combat", 0L);
        int pos = Utils.getParamValue(param, "pos", GuildParamKey.position_5);
        boolean isResult =  Utils.getParamValue(param, "isResult", false);

        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            if (isResult) {
                port.returns("guild", null, "memberList", null);
            }
            return;
        }
        if (humanIdGuildIdMap.containsKey(humanId)) {
            long gid = humanIdGuildIdMap.get(humanId);
            if (gid != guildId) {
                if (isResult) {
                    port.returns("guild", guildData.getGuild(), "memberList", guildData.getMemberList());
                }
                return;
            }
            if (guildData.getMember(humanId) != null) {
                if (isResult) {
                    port.returns("guild", guildData.getGuild(), "memberList", guildData.getMemberList());
                }
                return;
            }
        }
        if(humanId == guildData.getGuild().getLeaderId() && pos != GuildParamKey.position_1){
            pos = GuildParamKey.position_1;
        }

        GuildMember member = new GuildMember();
        member.setId(humanId);
        member.setHumanId(humanId);
        member.setName(name);
        member.setRoleHead(head);
        member.setGuildId(guildId);
        member.setOnline(online);
        member.setJoinTime(Port.getTime());
        member.setOfflineTime(Port.getTime());
        member.setCareer(pos);
        member.setPower(combat);
        member.persist();
        guildData.loadMember(member);
        humanIdGuildIdMap.put(humanId, guildId);
        // 处理申请人数据的更新
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getInfo(humanId);
        prx.listenResult(this::_result_getInfo, "guildId", guildId, "humanId", humanId);
        removeApply(humanId);
        guildData.updateCombat();
        guildData.addGuildLog(guildId, GuildParamKey.GuildLogType_JOIN, InformParam.getGuildLogMemberNameParamStr(humanId, name));// TODO 10003	GUILD_LOG_JOIN	1	成员加入	##1加入了本家族
//        if (guildData.getMemberNum() >= GlobalConfVal.guildFamilySignupNum) {
//            autoGuildLeague(guildData);
//        }
        updateRank(guildData);
        if (isResult) {
            port.returns("guild", guildData.getGuild(), "memberList", guildData.getMemberList());
        }
    }

    private void _result_getInfo(Param result, Param context) {
        HumanGlobalInfo info = result.get();
        long humanId = Utils.getParamValue(context, "humanId", 0L);
        long guildId = Utils.getParamValue(context, "guildId", 0L);
        GuildData guildData = guildMap.get(guildId);
        if (info != null) {
            guildData.addOnline(humanId);
        }
        List<Long> humanIdList = new ArrayList<>();
        humanIdList.add(humanId);
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.updateGuildInfo(humanIdList, guildData.getGuild());
    }

    @DistrMethod
    public void getGuild(long guildId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("guild", null, "memberList", null);
            return;
        }
        port.returns("guild", guildData.getGuild(), "memberList", guildData.getMemberList());
    }


    @DistrMethod
    public void searchGuild(int serverId, int type, String searchKey, int page){
        List<Define.p_guild> guildList = new ArrayList<>();
        List<Long> guildIdList = serverIdGuildIdListMap.getOrDefault(Config.SERVER_ID, new ArrayList<>());
        if (guildIdList.isEmpty()) {
            port.returns("guildList", guildList, "maxPage", 0);
            return;
        }
        int maxPage = guildIdList.size() / GuildParamKey.pageNum;
        maxPage = maxPage + (maxPage * GuildParamKey.pageNum == guildIdList.size() ? 0 : 1);
        if(type == GuildParamKey.searchType_1){
            for (long guildId : guildIdList) {
                GuildData guildData = guildMap.get(guildId);
                if (guildData == null) {
                    continue;
                }
                if(guildData.getGuild().getRemoveTime() > 0){
                    continue;
                }
                if(guildData.getMemberList().isEmpty()){
                    continue;
                }
                if (guildData.getGuildName().contains(searchKey)) {
                    guildList.add(GuildManager.inst().to_p_guild(guildData.getGuild(), guildData.getMemberList()).build());
                }
            }
        } else if(type == GuildParamKey.searchType_2){
            getGuildInfo(Config.SERVER_ID, GuildParamKey.searchType2Num);
            return;
        } else if(type == GuildParamKey.searchType_3){
            if (page > maxPage) {
                page = maxPage;
            }
            int i = (page - 1) * GuildParamKey.pageNum;
            int max = page * GuildParamKey.pageNum;
            int sumSize = guildIdList.size();
            i = Math.min(i, sumSize);
            i = Math.max(0, i);
            max = Math.min(max, sumSize);
            List<Long> tempList = guildIdList.subList(i, max);
            for (long guildId : tempList) {
                GuildData guildData = guildMap.get(guildId);
                if (guildData == null) {
                    continue;
                }
                if(guildData.getGuild().getRemoveTime() > 0){
                    continue;
                }
                if(guildData.getMemberList().isEmpty()){
                    continue;
                }
                if (guildData.getGuildName().contains(searchKey)) {
                    guildList.add(GuildManager.inst().to_p_guild(guildData.getGuild(), guildData.getMemberList()).build());
                    i++;
                }
                if (i >= max) {
                    break;
                }
            }
        }
        port.returns("guildList", guildList, "maxPage", maxPage);
    }

    @DistrMethod
    public void getGuildInfo(int serverId, int num){
        // TODO num = 6
        List<Define.p_guild> guildList = new ArrayList<>();
        List<GuildRankInfo> rankList = serverIdGuildRankListMap.getOrDefault(Config.SERVER_ID, new ArrayList<>());
        int size = rankList.size();
        if(size <= num){
            for(GuildRankInfo info : rankList){
                GuildData guildData = guildMap.get(info.guildId);
                if(guildData == null){
                    continue;
                }
                guildList.add(GuildManager.inst().to_p_guild(guildData.getGuild(), guildData.getMemberList()).build());
            }
            port.returns("guildList", guildList);
            return;
        }
        int average = size / num;
        List<Integer> indexList = new ArrayList<>();
        int indexOpen = 0;
        for(int i = 0; i < num; i++){
            int max = indexOpen + average;
            if(i == num - 1){
                max = size;
            }
            indexList.add(Utils.random(indexOpen, max));
            indexOpen = max;
        }

        for(int index : indexList){
            if(index >= size || index < 0){
                continue;
            }
            GuildRankInfo info = rankList.get(index);
            GuildData guildData = guildMap.get(info.guildId);
            guildList.add(GuildManager.inst().to_p_guild(guildData.getGuild(), guildData.getMemberList()).build());
        }
        int maxPage = guildList.size() / GuildParamKey.pageNum;
        maxPage = maxPage + (maxPage * GuildParamKey.pageNum == guildList.size() ? 0 : 1);
        port.returns("guildList", guildList, "maxPage", maxPage);
    }

    @DistrMethod
    public void getGuildIdList(int serverId){
        port.returns("guildIdList", serverIdGuildIdListMap.get(Config.SERVER_ID));
    }

    @DistrMethod
    public void guildJoin(long guildId, Param param){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", false, "tips", 149);
            return;
        }
        Guild guild = guildData.getGuild();
        int level = Utils.getParamValue(param, "level", 0);
        //加入等级限制
        if (guild.getJoinLimitLv() > level) {
            port.returns("result", false, "tips", 151);
            return;
        }
        int memberNum = guildData.getMemberList().size();
        ConfGuildLevel confGuildLevel = ConfGuildLevel.get(guild.getLevel());
        if (memberNum >= confGuildLevel.max_num) {
            port.returns("result", false, "tips", 152);
            return;
        }
        param.put("isResult", false);
        param.put("guildId", guildId);
        long humanId = param.get("humanId");
        if (humanIdGuildIdMap.containsKey(humanId)) {
            // 这边做个容错，如果你在被记录在某个宗门，那就直接更新玩家信息
            long oldGuildId = humanIdGuildIdMap.get(humanId);
            GuildData oldGuildData = guildMap.get(oldGuildId);
            if (guildData == null) {
                Log.temp.error("玩家挂载在某个家族旗下，但该家族已找不到相关GuildData对象，humanId={}，guild={}", humanId, oldGuildId);
                return;
            }
            List<Long> humanIdList = new ArrayList<>();
            humanIdList.add(humanId);
            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
            prx.updateGuildInfo(humanIdList, oldGuildData.getGuild());
        }
        //是否允许自动加入
        if (guild.getAutoJoin() == GuildParamKey.joinStatusAuto) {
            createGuildMember(param);
        } else {
            // 走申请流程
            createGuildApply(param);
        }
        port.returns("result", true, "tips", 0, "guildId", guildId);
    }

    private int isCanJoinStatus(long guildId, int level){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return GuildParamKey.joinStatusLose;
        }

        Guild guild = guildData.getGuild();
        //加入等级限制
        if(guild.getJoinLimitLv() > level){
            return GuildParamKey.joinStatusLose;
        }
        int memberNum = guildData.getMemberList().size();
        ConfGuildLevel confGuildLevel = ConfGuildLevel.get(guild.getLevel());
        if(memberNum >= confGuildLevel.max_num){
            return GuildParamKey.joinStatusLose;
        }
        //是否允许自动加入
        if(guild.getAutoJoin() == 1){
            return GuildParamKey.joinStatusAuto;
        }
        return GuildParamKey.joinStatusApply;
    }

    @DistrMethod
    public void createGuildApply(Param param){
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        String name = Utils.getParamValue(param, "name", "");
        long guildId = Utils.getParamValue(param, "guildId", 0L);
        String head = Utils.getParamValue(param, "head", "");
        int level = Utils.getParamValue(param, "level", 0);

        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildApply apply = new GuildApply();
        apply.setId(Port.applyId());
        apply.setHumanId(humanId);
        apply.setName(name);
        apply.setGuildId(guildId);
        apply.setRoleHead(head);
        apply.setLevel(level);
        apply.persist();
        guildData.loadApply(apply);
        addHumanIdApplyGuildIdMap(humanId, guildId);
        sendApplyToOnlineApproveMember(guildData);
    }

    /**
     * 给在线能审核的家族成员发申请列表
     */
    private void sendApplyToOnlineApproveMember(GuildData guildData) {
        List<Long> approveHumanIdList = new ArrayList<>();
        for (Long humanId : guildData.onlineHumanIdList) {
            int pos = guildData.getPosition(humanId);
            if (isPermission(pos, GuildParamKey.guildPermission_1)) {
                approveHumanIdList.add(humanId);
            }
        }
        if (approveHumanIdList.size() == 0) {
            return;
        }
        List<GuildApply> applyList = guildData.getApplyList();
        MsgGuild.guild_apply_s2c.Builder msg = MsgGuild.guild_apply_s2c.newBuilder();
        for (GuildApply apply : applyList) {
            msg.addApplyList(GuildManager.inst().to_p_guild_apply(apply));
        }
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.sendMsgTo(approveHumanIdList, msg.build());
    }

    /**
     * 一键申请，对于能快速加入的则直接加入，不能的时候再给所有符合的提申请
     */
    @DistrMethod
    public void guildQuickJoin(Param param){
        List<Long> guildIdList = serverIdGuildIdListMap.get(Config.SERVER_ID);
        if (guildIdList == null) {
            port.returns("result", new ReasonResult(false, ErrorTip.GuildEmpty));
            return;
        }
        int level = Utils.getParamValue(param, "level", 0);
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        long guildIdOld = humanIdGuildIdMap.getOrDefault(humanId, 0L);
        if (guildIdOld > 0) {
            port.returns("result", new ReasonResult(false, ErrorTip.GuildJoinOther));
            return;
        }

        long tempGuildId = 0L;
        for (long guildId : guildIdList) {
            if (isCanJoinStatus(guildId, level) == GuildParamKey.joinStatusAuto) {
                param.put("isResult", false);
                param.put("guildId", guildId);
                createGuildMember(param);
                tempGuildId = guildId;
                break;
            }
        }

        if (tempGuildId == 0L) {
            // 给所有符合条件的家族提交申请
            for (long guildId : guildIdList) {
                GuildData guildData = guildMap.get(guildId);
                Guild guild = guildData.getGuild();
                if (guild.getJoinLimitLv() > level) {
                    continue;
                }
                int memberNum = guildData.getMemberList().size();
                ConfGuildLevel confGuildLevel = ConfGuildLevel.get(guild.getLevel());
                if (memberNum >= confGuildLevel.max_num) {
                    continue;
                }
                GuildApply apply = guildData.getGuildApply(humanId);
                if (apply != null) {
                    continue;
                }
                param.put("isResult", false);
                param.put("guildId", guildId);
                createGuildApply(param);
                // 用来告诉客户端该宗门已申请
                HumanGlobalServiceProxy.newInstance().sendMsg(humanId, MsgGuild.guild_join_s2c.newBuilder().setGuildId(guildId).build());
            }
            port.returns("result", new ReasonResult(true));
        } else {
            // 有能直接加入的直接加入
            GuildData guildData = guildMap.get(tempGuildId);
            if (guildData == null) {
                port.returns("result", new ReasonResult(false, ErrorTip.GuildNotExist));
                return;
            }
            guildIdOld = humanIdGuildIdMap.get(humanId);
            if (guildIdOld != tempGuildId) {
                port.returns("result", new ReasonResult(false, ErrorTip.GuildJoinOther));
                return;
            }

            port.returns("result", new ReasonResult(true), "guildId", tempGuildId);
        }
    }


    public int getGuildPermissionByAction(int actionType) {
        switch (actionType) {
            case GuildParamKey.guildActionType_1:
                return GuildParamKey.guildPermission_2;
            case GuildParamKey.guildActionType_2:
                return GuildParamKey.guildPermission_6;
            case GuildParamKey.guildActionType_3:
                return GuildParamKey.guildPermission_7;
            case GuildParamKey.guildActionType_4:
                return GuildParamKey.guildPermission_8;
            case GuildParamKey.guildActionType_5:
                return GuildParamKey.guildPermission_9;
            case GuildParamKey.guildActionType_6:
                return GuildParamKey.guildPermission_10;
        }
        return 0;
    }

    @DistrMethod
    public void guildSetting(Param param){
        int k = Utils.getParamValue(param, "k", 0);
        int v = Utils.getParamValue(param, "v", 0);
        String s = Utils.getParamValue(param, "s", "");
        int serverId = Utils.getParamValue(param, "serverId", 0);
        long guildId = Utils.getParamValue(param, "guildId", 0L);
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        List<Define.p_key_value_string> lList = Utils.getParamValue(param, "list", new ArrayList());
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("result", new ReasonResult(false));
            return;
        }
        // 根据行为获取对应的权限要求
        int permissionType = getGuildPermissionByAction(k);
        if (permissionType == 0) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        int pos = guildData.getPosition(humanId);
        if (!isPermission(pos, permissionType)) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        Guild guild = guildData.getGuild();
        List<Long> noticeHumanIdList = new ArrayList<>(0);
        switch (k) {
            case GuildParamKey.guildActionType_1: {
                if(guild.getFreeGuildNameNum() > 0){
                    guild.setName(s);
                    guild.setFreeGuildNameNum(guild.getFreeGuildNameNum()-1);
                    syncGuildLeagueCross(guildData, new Param(GuildLeagueRecord.K.guildName, s));
                    port.returns("result", new ReasonResult(true), "guildMsg", GuildManager.inst().to_p_guild(guild, guildData.getMemberList()).build(),
                            "noticeHumanIdList", guildData.onlineHumanIdList);
                    return;
                }
                ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.公会改名消耗.SN);
                Map<Integer, Integer> costItemMap = new HashMap<>();
                int[] arr = Utils.strToIntArray(conf.strValue);
                if(arr == null || arr.length < 2){
                    port.returns("result", new ReasonResult(false));
                    return;
                }
                costItemMap.put(arr[0], arr[1]);
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                prx.checkAndConsume(humanId, costItemMap, MoneyItemLogKey.公会改名消耗);
                prx.listenResult(this::_result_checkAndConsume_changeName, "guildId", guildId, "s", s, "pid", port.createReturnAsync());
                return;
            }
            case GuildParamKey.guildActionType_2: {
                // 修改家族公告
                guild.setNotice(s);
                guild.update();
            }
            break;
            case GuildParamKey.guildActionType_3: {
                // 修改自动申请
                guild.setAutoJoin(v);
                guild.update();
            }
            break;
            case GuildParamKey.guildActionType_4: {
                // 修改等级限制
                guild.setJoinLimitLv(v);
                guild.update();
            }
            break;
            case GuildParamKey.guildActionType_5: {
                if (Utils.isEmptyJSONString(guild.getFlagJSON())) {
                    // 说明是第一次改，那就直接存
                    saveGuildFlag(guild, lList);
                    noticeHumanIdList.addAll(guildData.onlineHumanIdList);
                } else {
                    // 检测消耗
                    ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.公会旗帜改动消耗.SN);
                    Map<Integer, Integer> costItemMap = new HashMap<>();
                    int[][] costs = Utils.parseIntArray2(conf.strValue);
                    for (int i = 0; i < costs.length; i++)
                    {
                        int num = costItemMap.getOrDefault(costs[i][0], 0);
                        costItemMap.put(costs[i][0], num + costs[i][1]);
                    }
                    HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                    prx.checkAndConsume(humanId, costItemMap, MoneyItemLogKey.公会旗帜修改);
                    prx.listenResult(this::_result_checkAndConsume_changeFlag, "guildId", guildId, "list", lList, "pid", port.createReturnAsync());
                    return;
                }
            }
            break;
            case GuildParamKey.guildActionType_6: {
                JSONObject jo = Utils.toJSONObject(guild.getKvJSON());
                jo.put(String.valueOf(k), s);
                guild.setKvJSON(jo.toJSONString());
                guild.update();
            }
            break;
        }
        port.returns("result", new ReasonResult(true), "guildMsg", GuildManager.inst().to_p_guild(guild, guildData.getMemberList()).build(),
                "noticeHumanIdList", noticeHumanIdList);
    }

    private void _result_checkAndConsume_changeFlag(Param result, Param context) {
        long pid = Utils.getParamValue(context, "pid", 0L);
        ReasonResult rr = Utils.getParamValue(result, "result", new ReasonResult(false));
        if (!rr.success) {
            port.returnsAsync(pid,"result", new ReasonResult(false));
        }
        List<Define.p_key_value_string> lList = Utils.getParamValue(context, "list", new ArrayList<>());
        long guildId = Utils.getParamValue(context, "guildId", 0L);
        GuildData guildData = guildMap.get(guildId);
        Guild guild = guildData.getGuild();
        saveGuildFlag(guild, lList);
        port.returnsAsync(pid, "result", new ReasonResult(true), "guildMsg", GuildManager.inst().to_p_guild(guild, guildData.getMemberList()).build(),
                "noticeHumanIdList", guildData.onlineHumanIdList);
    }

    private void _result_checkAndConsume_changeName(Param result, Param context) {
        long pid = Utils.getParamValue(context, "pid", 0L);
        ReasonResult rr = Utils.getParamValue(result, "result", new ReasonResult(false));
        if (!rr.success) {
            port.returnsAsync(pid,"result", new ReasonResult(false));
        }
        String name = Utils.getParamValue(context, "s", "");
        long guildId = Utils.getParamValue(context, "guildId", 0L);
        GuildData guildData = guildMap.get(guildId);
        Guild guild = guildData.getGuild();
        guild.setName(name);
        guild.update();
        syncGuildLeagueCross(guildData, new Param(GuildLeagueRecord.K.guildName, name));
        port.returnsAsync(pid, "result", new ReasonResult(true), "guildMsg", GuildManager.inst().to_p_guild(guild, guildData.getMemberList()).build(),
                "noticeHumanIdList", guildData.onlineHumanIdList);
    }

    private void saveGuildFlag(Guild guild, List<Define.p_key_value_string> lList) {
        JSONArray jsonArray = new JSONArray();
        for (Define.p_key_value_string v : lList) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("K", v.getK());
            jsonObj.put("V", v.getV());
            jsonObj.put("S", v.getS());
            jsonArray.add(jsonObj);
        }
        guild.setFlagJSON(jsonArray.toJSONString());
        guild.update();

        syncGuildLeagueCross(guildMap.get(guild.getId()), new Param(GuildLeagueRecord.K.flagJSON, guild.getFlagJSON()));
    }

    @DistrMethod
    public void guildApprove(long guildId, long humanId, long applyUid, int type) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", new ReasonResult(false, ErrorTip.GuildNotExist));
            return;
        }
        GuildApply guildApply = guildData.getGuildApply(applyUid);
        if (guildApply == null) {
            port.returns("result", new ReasonResult(false, ErrorTip.GuildCancelApply));
            return;
        }
        int pos = guildData.getPosition(humanId);
        if (!isPermission(pos, GuildParamKey.guildPermission_1)) {
            port.returns("result", new ReasonResult(false, ErrorTip.GuildPosNotOperation));
            return;
        }

        long humanGuildId = humanIdGuildIdMap.getOrDefault(applyUid, 0L);
        if (humanGuildId != 0L) {
            guildData.removeApply(applyUid);
            removeApply(applyUid);
            port.returns("result", new ReasonResult(false, ErrorTip.GuildJoinOther));
            return;
        }

        if (type == GuildParamKey.agreeApply) { // 同意申请
            int status = isCanJoinStatus(guildId, guildApply.getLevel());
            if (status == GuildParamKey.joinStatusLose) {
                port.returns("result", new ReasonResult(false, ErrorTip.GuildNotMeetConditions));
                return;
            }
            long applyHumanId = guildApply.getHumanId();
            Param param = new Param();
            param.put("humanId", applyHumanId);
            param.put("name", guildApply.getName());
            param.put("guildId", guildId);
            param.put("head", guildApply.getRoleHead());
            param.put("level", guildApply.getLevel());
            param.put("isResult", false);


            long pid = port.createReturnAsync();
            MemberCallback.p_role_figureToJSONObject(applyHumanId, ret -> {
                if (ret.failed()) {
                    Log.human.error("获取玩家数据失败，humanId={}, {}", applyHumanId, ret.cause());
                    port.returnsAsync(pid, "result", new ReasonResult(false, ErrorTip.SystemDefault));
                    return;
                }
                JSONObject jo = ret.result();
                param.put("combat", new BigDecimal(jo.getLongValue(Human.K.combat)).longValue());
                param.put("name", jo.getString(Human.K.name));
                param.put("level", jo.getIntValue(Human.K.level));
                createGuildMember(param);
                sendApplyToOnlineApproveMember(guildData);
                port.returnsAsync(pid, "result", new ReasonResult(true), "applyUid", applyUid, "type", type);
            });
            return;
        } else if (type == GuildParamKey.denyApply) {// 拒绝申请
            guildData.removeApply(applyUid);
            List<Long> guildIdList = humanIdApplyGuildIdMap.get(applyUid);
            if (guildIdList != null && guildIdList.contains(guildId)) {
                guildIdList.remove(guildId);
            }
            sendApplyToOnlineApproveMember(guildData);
            HumanGlobalServiceProxy.newInstance().denyApply(applyUid, guildData.getGuild().getName());
        } else {
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        port.returns("result", new ReasonResult(true), "applyUid", applyUid, "type", type);
    }

    @DistrMethod
    public void guildKick(long guildId, long humanId, long kickId){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        int serverId = Util.getServerIdReal(guildData.getGuild().getGameServerId());
        GuildMember member = guildData.getMember(humanId);
        GuildMember kick = guildData.getMember(kickId);
        if (member == null || kick == null) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        int pos = guildData.getPosition(humanId);
        int posKick = guildData.getPosition(kickId);

        if (!isPermission(pos, GuildParamKey.guildPermission_4)) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        if (!isPosition(pos, posKick)) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        int week = Utils.getDayOfWeek();
        if(3 <= week && week <= 6){// 周二到周五不允许退宗门
            port.returns("result", new ReasonResult(false), "tips", 508);
            return;
        }
        guildData.removeMember(kickId);
        guildData.savePosHumanIdMap(true);
        humanIdGuildIdMap.remove(kickId);
        guildData.addGuildLog(guildId, GuildParamKey.GuildLogType_REMOVE, InformParam.getGuildLogMemberNameParamStr(member.getHumanId(), member.getName()),
                InformParam.getGuildLogMemberNameParamStr(kick.getHumanId(), kick.getName()));// TODO 10002	GUILD_LOG_REMOVE	1	逐出成员	##1将##2逐出了家族

        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.leaveGuild(kickId, guildId);

        leaveGuildSendBroadcast(guildData, kickId);

        removeGuildRank(guildId, serverId);
        ActivityManager.inst().leaveGuild(kickId, guildId);
        port.returns("result", new ReasonResult(true), "kickId", kickId);
    }

    private void removeGuildRank(long guildId, int serverId){
        guildIdRankInfoMap.remove(guildId);

        List<GuildRankInfo> rankLvList = serverIdGuildRankListMap.getOrDefault(Config.SERVER_ID, new ArrayList<>());

        Iterator<GuildRankInfo> iteratorLv = rankLvList.iterator();
        while (iteratorLv.hasNext()) {
            GuildRankInfo info = iteratorLv.next();
            if(info.guildId == guildId){
                iteratorLv.remove();
                break;
            }
        }

        List<GuildRankInfo> rankCombatList = serverIdGuildCombatRankListMap.getOrDefault(Config.SERVER_ID, new ArrayList<>());
        Iterator<GuildRankInfo> iterator = rankCombatList.iterator();
        while (iterator.hasNext()) {
            GuildRankInfo info = iterator.next();
            if(info.guildId == guildId){
                iterator.remove();
                break;
            }
        }

    }

    /**
     * 是否有权限
     * <AUTHOR>
     * @Date 2024/4/10
     * @Param
     */
    private boolean isPermission(int pos, int permission){
        ConfGuildCareer conf = ConfGuildCareer.get(pos);
        if(conf == null){
            return false;
        }
        if(conf.permissions == null){
            return false;
        }
        List<Integer> list = Utils.intArrToList(conf.permissions);
        if(list.contains(permission)){
            return true;
        }
        return false;
    }

    /**
     * 是否有权管理职位
     * <AUTHOR>
     * @Date 2024/4/10
     * @Param
     */
    private boolean isPosition(int pos, int lowJobPos){
        ConfGuildCareer conf = ConfGuildCareer.get(pos);
        if (conf == null) {
            return false;
        }
        if (conf.permissions == null) {
            return false;
        }
        List<Integer> list = Utils.intArrToList(conf.low_job);
        if (list.contains(lowJobPos)) {
            return true;
        }
        return false;
    }

    @DistrMethod
    public void guildLeave(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("reason", 149);
            return;
        }
        /*if(!Utils.isSameWeek2(Util.getOpenServerTime(C.GAME_SERVER_ID), Port.getTime())){
            long time = Port.getTime();
            long startTime = GuildLeagueUtils.getStepStartTime(ParamKey.guild_gvg_step_2);
            long endTime = GuildLeagueUtils.getStepEndTime(ParamKey.guild_gvg_step_14);
            if (startTime > time && time<=endTime){
                port.returns("reason", 207);
                return;
            }
        }*/
        int week = Utils.getDayOfWeek();
        if(3 <= week && week <= 6){// 周二到周五不允许退宗门
            port.returns("reason", 207);
            return;
        }

        GuildMember member = guildData.getMember(humanId);
        if(member == null){
            port.returns("reason", 156);
            return;
        }
        int pos = guildData.getPosition(humanId);
        if(pos == GuildParamKey.position_1){// 会长
            long nextLeaderId = guildData.getNextLeaderId();
            if(nextLeaderId == 0){
                removeGuild(guildId);
                humanIdGuildIdMap.remove(humanId);
                leaveGuildSendBroadcast(guildData, humanId);
                ActivityManager.inst().leaveGuild(humanId, guildId);
                port.returns("reason", 0);
                return;
            }
            guildData.getGuild().setLeaderId(nextLeaderId);
            guildData.getGuild().setLeaderChangeTime(Port.getTime());
            Param param = new Param();
            param.put(GuildLeagueRecord.K.leaderId, nextLeaderId);
            param.put(GuildLeagueRecord.K.leaderName, guildData.getMember(nextLeaderId).getName());
            syncGuildLeagueCross(guildData, param);
        }
        guildData.removeMember(humanId);
        guildData.savePosHumanIdMap(true);
        humanIdGuildIdMap.remove(humanId);
        guildData.addGuildLog(guildId, GuildParamKey.GuildLogType_EXIT, InformParam.getGuildLogMemberNameParamStr(humanId, member.getName()));// TODO 10001	GUILD_LOG_EXIT	1	家族成员退出	##1退出了本家族
        leaveGuildSendBroadcast(guildData, humanId);
        ActivityManager.inst().leaveGuild(humanId, guildId);



        port.returns("reason", 0);
    }

    private void leaveGuildSendBroadcast(GuildData guildData, long humanId){
        HumanData.getHumanDataAsync(humanId, HumanManager.inst().humanClasses2, result -> {
            if(!result.succeeded()){
                Log.human.error("获取玩家数据失败，humanId={}, {}", humanId, result.cause());
                return;
            }
            HumanData humanData = result.result();
            if(humanData == null){
                return;
            }
            Define.p_guild_area_member.Builder dInfo = Define.p_guild_area_member.newBuilder();
            dInfo.setRoleId(humanId);
            Define.p_pos.Builder dPos = Define.p_pos.newBuilder();
            dPos.setX(0);
            dPos.setY(0);
            dInfo.setPos(dPos);
            dInfo.setBreak(0);

            if (humanData != null) {
                dInfo.setRoleId(humanId);
                dInfo.setRoleName(humanData.human.getName());
                dInfo.setFigure(HumanManager.inst().to_p_role_figure(humanData.human, humanData.human2));
            } else {
                // 离开没用，只是为了协议不报错，数据什么的无所谓
                dInfo.setRoleName("");
                Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
                //遍历weapon到face的部位不存在或者value=0就去默认取
                for(int i = weapon; i <= face; ++i){
                    dFigur.addEquipList(HumanManager.inst().to_p_key_value(i,0));
                }
                // 翅膀
                dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, 0));

                dFigur.setHairFigure(0);// 发色
                dFigur.setJobFigure(0);
                // 坐骑
                dFigur.setMountFigure(0);
                // 神器sn
                dFigur.setArtifactFigure(0);
                dFigur.setGender(1);// 无性别 默认1
                // 皮肤
                dFigur.addSkinList(HumanManager.inst().to_p_key_value(2, 0));
                // 头衔
                dFigur.setCurrentTitle(0);
                dInfo.setFigure(dFigur);
            }
            MsgGuild.guild_area_broadcast_s2c.Builder msg = MsgGuild.guild_area_broadcast_s2c.newBuilder();
            msg.setType(GuildParamKey.broadcast_type_4);
            msg.setActionMember(dInfo);

            // 排除自己通知当前工会里的人
            List<Long> areaHumanIdList = new ArrayList<>(guildData.areaHumanIdList);
            areaHumanIdList.remove(humanId);
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendMsgTo(areaHumanIdList, msg.build());
        });
    }

    @DistrMethod
    public void removeGuild(long guildId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        nameList.remove(guildData.getGuild().getName());
        Log.temp.error("删除公会，guildId={}， name={}", guildId, guildData.getGuild().getName());
        List<Long> memberIdList = guildData.getHumanIdList();
        for(long humanId : memberIdList){
            guildData.removeMember(humanId);
            humanIdGuildIdMap.remove(humanId);
        }
        List<Long> humanIdList = guildData.removeApplyAll();
        for(long humanId : humanIdList){
            List<Long> guildIdList = humanIdApplyGuildIdMap.get(humanId);
            if(guildIdList != null && guildIdList.contains(guildId)){
                guildIdList.remove(guildId);
            }
        }
        guildData.savePosHumanIdMap(false);
        guildData.getGuild().setRemoveTime(Port.getTime());
        guildData.getGuild().update();

        int serverIdOld = guildData.getGuild().getGameServerId();
        List<Long> guildIdList = serverIdGuildIdListMap.computeIfAbsent(Config.SERVER_ID, k -> new ArrayList<>());
        if (guildIdList.contains(guildId)){
            guildIdList.remove(guildId);
        }

        // 剔除排行榜
        List<GuildRankInfo> rankInfoList = serverIdGuildRankListMap.getOrDefault(Config.SERVER_ID, new ArrayList<GuildRankInfo>());
        Iterator<GuildRankInfo> iterator = rankInfoList.iterator();
        while (iterator.hasNext()) {
            GuildRankInfo rankInfo = iterator.next();
            if (rankInfo.guildId == guildData.getId()) {
                iterator.remove();
            }
        }

        guildMap.remove(guildId);


        if (!memberIdList.isEmpty()) {
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.leaveGuild(memberIdList, guildId);

            // 给成员发解散
            MsgGuild.guild_dissolve_s2c.Builder dissolveMsg = MsgGuild.guild_dissolve_s2c.newBuilder();
            proxy.sendMsgTo(memberIdList, dissolveMsg.build());
        }

        GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
        proxy.removeGuild(guildId);

        if(GuildLeagueUtils.isBridge(serverIdOld)){
            try {
                CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, serverIdOld, res -> {
                    try {
                        if(res.failed()){
                            Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                            return;
                        }
                        CrossPoint result = res.result();
                        GuildLeagueServiceProxy proxyCross = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                        proxyCross.removeGuild(guildId);
                    } catch (Exception e){
                        Log.temp.error("==跨服链接出问题 ", e);
                    }
                });
            } catch (Exception e){
                Log.temp.error("====跨服获取数据出问题", e);
            }
        }
    }



    @DistrMethod
    public void guildDissolve(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("reason", 149);
            return;
        }
        if(humanId != guildData.getGuild().getLeaderId()){
            return;
        }
        removeGuild(guildId);
        port.returns("reason", 0);
    }

    @DistrMethod
    public void guildChangePos(long guildId, long humanId, long targetId, int pos) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        if (pos <= 0 || pos > GuildParamKey.position_Max) {
            port.returns("result", new ReasonResult(false));
            return;
        }
        Guild guild = guildData.getGuild();
        // TODO 职位设置权限
        if (humanId != guild.getLeaderId()) {
            port.returns("result", new ReasonResult(false, "148"));
            return;
        }
        int posMy = guildData.getPosition(humanId);

        if (!isPermission(posMy, GuildParamKey.guildPermission_3)) {
            port.returns("result", new ReasonResult(false, "148"));
            return;
        }
        int posTar = guildData.getPosition(targetId);
        if (!isPosition(posMy, posTar)) {
            port.returns("result", new ReasonResult(false, "148"));
            return;
        }
        if (pos == GuildParamKey.position_1) {
            if(posMy != GuildParamKey.position_1 || humanId != guildData.getGuild().getLeaderId()){
                port.returns("result", new ReasonResult(false, "148"));
                return;
            }
            long cd = ConfGlobal.get(ConfGlobalKey.更换会长时间.SN).value * Time.SEC;
            if(Port.getTime() - guild.getLeaderChangeTime() < cd){
                port.returns("result", new ReasonResult(false, "225"));
                return;
            }
        }
        GuildMember member = guildData.getMember(targetId);
        GuildMember memberMy = guildData.getMember(humanId);
        if (member == null || memberMy == null) {
            return;
        }
        member.setCareer(pos);
        guildData.updatePosHumanIdMap(posTar, pos, targetId);

        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.updateGuildPosition(targetId, pos);

        List<GuildMember> changeMemberList = new ArrayList<>();
        changeMemberList.add(member);
        Define.p_guild pGuildMsg = null;
        if (pos == GuildParamKey.position_1) {
            changeMemberList.add(memberMy);
            guild.setLeaderId(member.getHumanId());
            guild.setLeaderChangeTime(Port.getTime());
            pGuildMsg = GuildManager.inst().to_p_guild(guildData.getGuild(), guildData.getMemberList()).build();

            guildData.updatePosHumanIdMap(GuildParamKey.position_1, GuildParamKey.position_5, humanId);

            memberMy.setCareer(GuildParamKey.position_5);

            HumanGlobalServiceProxy proxyMy = HumanGlobalServiceProxy.newInstance();
            proxyMy.updateGuildPosition(humanId, GuildParamKey.position_5);

            guildData.addGuildLog(guildId, GuildParamKey.GuildLogType_TRANSFER,
                    InformParam.getGuildLogMemberNameParamStr(memberMy.getHumanId(), memberMy.getName()),
                    InformParam.getGuildLogMemberNameParamStr(member.getHumanId(), member.getName()));// 10006 GUILD_LOG_TRANSFER 1 转让族长 ##1将族长转让给##2

            Param param = new Param();
            param.put(GuildLeagueRecord.K.leaderId, member.getHumanId());
            param.put(GuildLeagueRecord.K.leaderName, member.getName());
            syncGuildLeagueCross(guildData, param);
        } else {
            int logType;
            JSONObject param = new JSONObject();
            param.put("conf", ConfGuildCareer.class.getSimpleName());
            param.put("field", "name");
            if (posTar < pos) {
                logType = GuildParamKey.GuildLogType_RELIEVE;
                param.put("sn", posTar);
            } else {
                logType = GuildParamKey.GuildLogType_RESIGN;
                param.put("sn", pos);
            }
            guildData.addGuildLog(guildId, logType,
                    InformParam.getGuildLogMemberNameParamStr(memberMy.getHumanId(), memberMy.getName()),
                    InformParam.getGuildLogMemberNameParamStr(member.getHumanId(), member.getName()),
                    param);
        }
        guild.update();
        port.returns("result", new ReasonResult(true), "changeMemberList", changeMemberList, "guildMsg", pGuildMsg);
    }

    @DistrMethod
    public void guildMember(long guildId){
        GuildData guildData = guildMap.get(guildId);
        // TODo
        port.returns("change_type", 0, "memberList", guildData.getMemberList());
    }

    @DistrMethod
    public void getGuildId(long humanId){
        port.returns("guildId", humanIdGuildIdMap.getOrDefault(humanId, 0L));
    }


    @DistrMethod
    public void guildDonate(long guildId, long humanId, int addExp){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        if(addExp <= 0){
            return;
        }
        Guild guild = guildData.getGuild();
        GuildMember member = guildData.getMember(humanId);
        if(member == null){
            return;
        }
        //累计捐赠的总额
        member.setDonateSum(member.getDonateSum() + addExp);
        //本周捐赠的总额
        member.setDonateWeek(member.getDonateWeek() + addExp);
        //捐赠的次数
        member.setDonateCount(member.getDonateCount() + 1);
        member.update();
        guildData.addExp(addExp,true, humanId);
    }
    @DistrMethod
    public void guildDonateInfo(long guildId, long humanId) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("donateSum", 0L, "donateWeek", 0L, "donateCount", 0);
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            port.returns("donateSum", 0L, "donateWeek", 0L, "donateCount", 0);
            return;
        }
        port.returns("donateSum", member.getDonateSum(), "donateWeek", member.getDonateWeek(), "donateCount", member.getDonateCount());
    }


    @DistrMethod
    public void getGuildApplyList(long guildId, long humanId){
        // 申请
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("applyGuildIdList", new ArrayList<GuildApply>());
            return;
        }
        int pos = guildData.getPosition(humanId);
        if (!isPermission(pos, GuildParamKey.guildPermission_1)) {
            port.returns("applyGuildIdList", new ArrayList<GuildApply>());
            return;
        }
        port.returns("applyGuildIdList", guildData.getApplyList());
    }

    @DistrMethod
    public void applyGuildIdList(long humanId){
        // 申请
        port.returns("applyGuildIdList", humanIdApplyGuildIdMap.get(humanId));
    }

    public void removeApply(long humanId){
        List<Long> guildIdList = humanIdApplyGuildIdMap.get(humanId);
        if(guildIdList == null || guildIdList.isEmpty()){
            return;
        }
        for(long guildId : guildIdList){
            GuildData guildData = guildMap.get(guildId);
            if(guildData == null){
                continue;
            }
            guildData.removeApply(humanId);
        }

        humanIdApplyGuildIdMap.remove(humanId);
    }

    @DistrMethod
    public void guildLog(long guildId, long guildLogId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        List<GuildLog> logList = guildData.getGuildLogList();
        boolean startAdd = guildLogId == 0;
        List<GuildLog> guildLostList = new ArrayList<>();
        for (int i = 0; i < logList.size(); i++) {
            if (logList.get(i).getId() == guildLogId) {
                startAdd = true;
                continue;
            }
            if (startAdd) {
                guildLostList.add(logList.get(i));
            }
        }
        guildLostList.sort(Comparator.comparingLong(GuildLog::getTime));
        Collections.reverse(guildLostList);
        List<Define.p_guild_log> logMsgList = new ArrayList<>();
        for (GuildLog log : guildLostList) {
            logMsgList.add(GuildManager.inst().to_p_guild_log(log, guildData));
        }
        port.returns("logList", logMsgList);
    }

    @DistrMethod
    public void enterGuildArea(long guildId){
        int mapSn = 2;
//        long
//        long stageID = Port.applyIid();
//        String portId = StageManager.inst().getStagePortId();
//        StageServiceProxy proxy = StageServiceProxy.newInstance(Distr.getNodeId(portId), portId, D.SERV_STAGE_DEFAULT);
//        proxy.createGuildStage(stageID, mapSn, new Param());
//        proxy.listenResult(this::_result_createGuildStage, "pid", pid, "humanId", humanId, "param", param);
    }

    @DistrMethod
    public void getGuildInfo(long guildId, long humanId){
        long guildIdTemp = humanIdGuildIdMap.getOrDefault(humanId, 0L);
        if(guildIdTemp != guildId && guildIdTemp != 0){
            guildId = guildIdTemp;
        }
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("guildId", 0L, "guildLv", 0, "guildName", "", "guildPosition", 0);
            return;
        }
        if (guildData.getGuild().getRemoveTime() > 0) {
            port.returns("guildId", 0L, "guildLv", 0, "guildName", "", "guildPosition", 0);
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            port.returns("guildId", 0L, "guildLv", 0, "guildName", "", "guildPosition", 0);
            return;
        }
        Guild guild = guildData.getGuild();
        int level = guild.getLevel();
        String name = guild.getName();
        port.returns("guildId", guildId, "guildLv", level, "guildName", name, "guildPosition", guildData.getPosition(humanId));
    }

    @DistrMethod
    public void humanLogout(long guildId, long humanId, byte[] brief) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            return;
        }
        member.setOnline(0);
        member.setOfflineTime(Port.getTime());
        guildData.removeOnline(humanId);
        // 玩家下线的时候，如果是报名胖头鱼的玩家，把他的信息缓存起来
        if (guildData.gveSignList.contains(humanId)) {
            guildData.updateGveBriefCache(humanId, brief);
        }
    }

    @DistrMethod
    public void humanLogin(long guildId, long humanId) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            return;
        }
        member.setOnline(1);
        member.setOfflineTime(Port.getTime());
        guildData.addOnline(humanId);
        // 玩家登陆的时候，如果是报名胖头鱼的玩家并且胖头鱼还没开始准备，则把之前缓存的信息清除
        if (guildData.gveSignList.contains(humanId) && !guildGveReady) {
            guildData.removeGveHumanDataCache(humanId);
        }
    }

    @ScheduleMethod(DataResetService.CRON_DAY_HOUR)
    public void _CRON_DAY_HOUR() {
        // 检测是否自动报名
        autoGuildLeague();
    }

    @ScheduleMethod(DataResetService.CRON_WEEK_345_hour_22)
    public void _CRON_WEEK_345_hour_22(){
        for(GuildData guildData : guildMap.values()){
            EntityManager.getEntityListAsync(GuildLog.class, guildData.getId(), (res)-> {
                if (!res.succeeded()) {
                    //加载失败
                    Log.temp.error("===加载GuildLog失败，group={}", guildData.getId());
                    return;
                }
                List<GuildLog> modelGuildLogList = res.result();
                for(GuildLog log : modelGuildLogList) {
                    guildData.loadLog(log);
                }
            });
        }
    }

    @ScheduleMethod(DataResetService.CRON_DAY_ZERO)
    public void _CRON_DAY_ZERO() {
        List<GuildData> guildDataList = new ArrayList<>(guildMap.values());
        List<String> keyDelList = new ArrayList<>();
        for(GuildData guildData : guildDataList){
            zeroResetGuild(guildData);

            keyDelList.add(RedisKeys.guildBoxRank + guildData.getId());
        }

//        initTabale();//无宝箱功能
        // 检测是否自动报名
//        checkAutoGuildLeague();
        initGveTime();
        gveTodayRewardHumanIdSet.clear();
        initBoxTickTimer();

        GuildLeagueUtils.initStepTime();

        if(!keyDelList.isEmpty()){
            EntityManager.redisClient.del(keyDelList, r->{});
        }

        if(Utils.getDayOfWeek() == 2){
            enrollGuildIdSet.clear();
        }
    }

    /**
     * 工会重置
     * @param guildData
     */
    private void zeroResetGuild(GuildData guildData){
        Guild guild = guildData.getGuild();
        guild.setBossBoxNum(0);
        guild.setBossHighBoxNum(0);
        guild.setDailyExp(0);
        checkGuildAutoSwapLeader(guildData);
        guild.setZeroResetTime(Port.getTime());
        guild.update();

        guildData.guildList.clear();
        guildData.gveSignList.clear();
        guildData.gveJoinIdList.clear();
        guildData.resetMemberBossMaxHurt();
        guildData.resetWeekOne();
    }

    /**
     * 自动换会长
     * <AUTHOR>
     * @Date 2024/4/23
     * @Param
     */
    private void checkGuildAutoSwapLeader(GuildData guildData){
        Guild guild = guildData.getGuild();
        long leaderId = guild.getLeaderId();
        if(guildData == null){
            return;
        }
        GuildMember member = guildData.getMember(leaderId);
        if(member == null){
            return;
        }
        if(member.getOnline() == 1){
            return;
        }
        if(member.getOfflineTime() == 0){
            member.setOfflineTime(guild.getCreateTime());
            member.update();
        }
        long time = (Port.getTime() - member.getOfflineTime()) / Time.SEC;
        long limitTime = ConfGlobal.get(ConfGlobalKey.更换会长时间.SN).value;
        if(time < limitTime){
            return;
        }

        long humanId = guildData.getNextLeaderId();
        if (humanId <= 0) {
            return;
        }
        GuildMember memberNew = guildData.getMember(humanId);
        int newLeaderOldPos = memberNew.getCareer();
        member.setCareer(GuildParamKey.position_5);
        memberNew.setCareer(GuildParamKey.position_1);
        guild.setLeaderId(humanId);

        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.updateGuildPosition(humanId, GuildParamKey.position_1);// 只用给新族长尝试发更新就行，老族长一定不在线

        guildData.updatePosHumanIdMap(newLeaderOldPos, GuildParamKey.position_1, memberNew.getHumanId());
        guildData.updatePosHumanIdMap(GuildParamKey.position_1, GuildParamKey.position_5, member.getHumanId());

        guildData.addGuildLog(guild.getId(), GuildParamKey.GuildLogType_TRANSFER, InformParam.getGuildLogMemberNameParamStr(member.getHumanId(), member.getName())
                , InformParam.getGuildLogMemberNameParamStr(memberNew.getHumanId(), memberNew.getName()));

        Param param = new Param();
        param.put(GuildLeagueRecord.K.leaderId, memberNew.getHumanId());
        param.put(GuildLeagueRecord.K.leaderName, memberNew.getName());
        syncGuildLeagueCross(guildData, param);
    }

    @DistrMethod
    public void updateMemberCombat(long guildId, long humanId, Param param){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if(member == null){
            return;
        }
        if(param.containsKey("level")){
            member.setLevel(param.getInt("level"));
        }
        if(param.containsKey("name")){
            member.setName(param.getString("name"));
        }
        if(param.containsKey("combat")){
            guildData.updateCombat(humanId, param.getLong("combat"));
            updateRank(guildData);
        }

    }

    private void updateRank(GuildData guildData){
        updateRank(guildData, true);
    }

    private void updateRank(GuildData guildData, boolean isCheckRank){
        GuildRankInfo rankInfo = guildIdRankInfoMap.get(guildData.getId());
        if(rankInfo == null){
            rankInfo = new GuildRankInfo(guildData.getId(), guildData.getGuild().getLevel(), guildData.getSumCombat(), "");
            guildIdRankInfoMap.put(guildData.getId(), rankInfo);
            addRank(guildData.getServerId(), rankInfo);
            addRankCombat(guildData.getServerId(), rankInfo);
            if(isCheckRank){
                checkRank(guildData.getServerId());
                checkRankCombat(guildData.getServerId());
            }

        } else {
            rankInfo.level = guildData.getGuild().getLevel();
            rankInfo.combat = guildData.getSumCombat();
            addRank(guildData.getServerId(), rankInfo);
            addRankCombat(guildData.getServerId(), rankInfo);
            if(isCheckRank){
                checkRank(guildData.getServerId());
                checkRankCombat(guildData.getServerId());
            }
        }

    }

    private void addRank(int serverId, GuildRankInfo rankInfo){
        List<GuildRankInfo> rankList = serverIdGuildRankListMap.get(Config.SERVER_ID);
        if(rankList == null){
            rankList = new ArrayList<>();
            serverIdGuildRankListMap.put(Config.SERVER_ID, rankList);
        }
        GuildRankInfo rankInfoOld = null;
        for(GuildRankInfo info : rankList){
            if(info.guildId == rankInfo.guildId){
                rankInfoOld = info;
                break;
            }
        }
        if(rankInfoOld != null){
            rankList.remove(rankInfoOld);
        }
        rankList.add(rankInfo);
    }

    private void checkRank(int serverId){
        List<GuildRankInfo> rankList = serverIdGuildRankListMap.get(Config.SERVER_ID);
        if(rankList == null){
            return;
        }
        Collections.sort(rankList, (a, b) -> {
            int ret = 0;// 0默认相等
            if (a != null && b != null) {
                // 倒序
                if (a.level < b.level) {
                    ret = 1;
                } else if (a.level > b.level) {
                    ret = -1;
                } else {
                    // 正序
                    if (a.guildId < b.guildId) {
                        ret = -1;
                    } else if (a.guildId > b.guildId) {
                        ret = 1;
                    }
                }
            }
            return ret;
        });
    }
    private void addRankCombat(int serverId, GuildRankInfo rankInfo){
        List<GuildRankInfo> rankList = serverIdGuildCombatRankListMap.get(Config.SERVER_ID);
        if(rankList == null){
            rankList = new ArrayList<>();
            serverIdGuildCombatRankListMap.put(Config.SERVER_ID, rankList);
        }
        Iterator<GuildRankInfo> iterator = rankList.iterator();
        while (iterator.hasNext()) {
            GuildRankInfo info = iterator.next();
            if(info.guildId == rankInfo.guildId){
                iterator.remove();
                break;
            }
        }
        rankList.add(rankInfo);
    }
    private void checkRankCombat(int serverId){
        checkRankCombat(serverId, false);
    }
    private void checkRankCombat(int serverId, boolean isInit){
        List<GuildRankInfo> rankList = serverIdGuildCombatRankListMap.get(Config.SERVER_ID);
        if(rankList == null){
            return;
        }
        long combatTop = rankList.get(0).combat;
        Collections.sort(rankList, (a, b) -> {
            int ret = 0;// 0默认相等
            if (a != null && b != null) {
                // 倒序
                if (a.combat < b.combat) {
                    ret = 1;
                } else if (a.combat > b.combat) {
                    ret = -1;
                } else {
                    // 倒序
                    if (a.level < b.level) {
                        ret = 1;
                    } else if (a.level > b.level) {
                        ret = -1;
                    }
                }
            }
            return ret;
        });

        long newCombatTop = rankList.get(0).combat;
        if(newCombatTop != combatTop || isInit){
            AdminRedis.setAndExpire(RedisKeys.arenaCrossServerIdCombat + Utils.getServerIdTo(Config.SERVER_ID), String.valueOf(newCombatTop), Time.DAY_30_SEC);
        }
    }

    /**
     * 获取最强公会旗帜
     * @param serverId
     */
    @DistrMethod
    public void getTopCombatGuildFlagList(int serverId){
        List<GuildRankInfo> rankList = serverIdGuildCombatRankListMap.get(Config.SERVER_ID);
        if(rankList == null || rankList.isEmpty()){
            port.returns("result", false);
            return;
        }
        long guildId = rankList.get(0).guildId;
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.temp.error("未找到公会id={}", guildId);
            port.returns("result", false);
            return;
        }
        Guild guild = guildData.getGuild();
        if(guild == null){
            Log.temp.error("未找到公会id={}", guildId);
            port.returns("result", false);
            return;
        }
        String flagJSON = guild.getFlagJSON();
        List<Define.p_key_value_string> flagList = new ArrayList<>();
        if (!Utils.isEmptyJSONString(flagJSON)) {
            flagList = GuildManager.inst().to_p_guild_flag(flagJSON);
        }
        port.returns("result", true, "flagList", flagList);
    }

    @DistrMethod
    public void getRankList(int serverId, int page, long myGuildId, int rankSn){
        List<Integer> serverIdList = Util.getMergeServerIdList(Config.SERVER_ID);
        if(serverIdList.size() > 1){
            serverId = Config.SERVER_ID;
        }
        if(rankSn == RankParamKey.rankTypeGuildCombat_1014){
            getRankList(serverId, page, myGuildId);
            return;
        }
        guildRankList(serverId, page, myGuildId, rankSn);
    }

    @DistrMethod
    public void getRankList(int serverId, int page, long myGuildId){
        List<Integer> serverIdList = Util.getMergeServerIdList(Config.SERVER_ID);
        if(serverIdList.size() > 1){
            serverId = Config.SERVER_ID;
        }
        ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeGuildCombat_1014);
        int openIndex = (page - 1) * RankParamKey.pageNum;

        MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();

        List<GuildRankInfo> rankList = serverIdGuildCombatRankListMap.get(serverId);
        checkRankCombat(serverId);
        if (rankList == null || openIndex > rankList.size()) {
            port.returns("rankList", msg.build());
            return;
        }
        int size = rankList.size();
        if(openIndex > size){
            port.returns("rankList", msg.build());
            return;
        }
        int endIndex = page * RankParamKey.pageNum - 1;
        if(endIndex > confRanktype.show_num){
            endIndex = confRanktype.show_num;
        }
        endIndex = Math.min(endIndex, rankList.size());
        boolean isUp = rankHumanDataMap.isEmpty() || (rankUpTime + 5 * Time.MIN < Port.getTime());

        List<GuildRankInfo> list = rankList.subList(openIndex, endIndex);

        List<Long> humanIdList = new ArrayList<>();
        if(openIndex < 3){
            int temp = 0;
            Iterator<GuildRankInfo> iterator = list.iterator();
            while (iterator.hasNext()) {
                GuildRankInfo info = iterator.next();
                temp++;
                GuildData guildData = guildMap.get(info.guildId);
                if(guildData == null){
                    Log.temp.error("未找到公会id={}", info.guildId);
                    continue;
                }
                Guild guild = guildData.getGuild();
                if(guild == null){
                    Log.temp.error("未找到公会id={}", info.guildId);
                    continue;
                }
                HumanData humanData = rankHumanDataMap.get(guild.getLeaderId());
                if((isUp && temp <= 3) || (temp <= 3 && humanData == null)){
                    humanIdList.add(guild.getLeaderId());
                }
                if(temp >= 3){
                    break;
                }
            }
        }
        int maxPage = (int) Math.ceil((double) size / RankParamKey.pageNum);
        Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
        rankData.setType(RankParamKey.rankTypeGuildCombat_1014);
        rankData.setServer(Utils.getServerIdTo(serverId));
        rankData.setPage(page);
        rankData.setMaxPage(maxPage <= 0 ? 1 : maxPage);
        rankData.setTotalNum(size);
        rankData.setNextFreshTime(confRanktype.refresh_time);

        // 无需获取
        if(humanIdList.isEmpty()){
            addGuildRankData(openIndex, list, myGuildId, rankData, serverId);
            msg.addRankDataList(rankData);
            port.returns("rankList", msg.build());
            return;
        }

        long pid = port.createReturnAsync();
        // 异步获取头几名玩家数据
        int finalServerId = serverId;
        HumanData.getList(humanIdList, new Class[]{Human.class, Human2.class}, (ret) -> {
            if (ret.failed()) {
                Log.temp.error("无法获取前三名公会玩家数据 humanIds={}", humanIdList);
                port.returnsAsync(pid, "rankList", msg.build());
                return;
            }
            List<HumanData> humanDataList = ret.result();
            if(humanDataList == null){
                Log.temp.error("无法获取前三名公会玩家数据humanIds={}", humanIdList);
                return;
            }
            for(HumanData humanData : humanDataList){
                if(humanData == null){
                    continue;
                }
                rankHumanDataMap.put(humanData.human.getId(), humanData);
            }
            rankUpTime = Port.getTime();
            humanDataList.clear();

            addGuildRankData(openIndex, list, myGuildId, rankData, finalServerId);
            msg.addRankDataList(rankData);
            port.returnsAsync(pid, "rankList", msg.build());
        });
    }




    private void addGuildRankData(int openIndex, List<GuildRankInfo> list, long myGuildId,  Define.p_rank_data.Builder rankData, int serverId) {
        int rank = openIndex;
        HumanData defaultHumanData = null;
        if(list == null || list.isEmpty()){
            return;
        }

        boolean isMyguild = true;
        Iterator<GuildRankInfo> iterator = list.iterator();
        while (iterator.hasNext()) {
            GuildRankInfo info = iterator.next();
            GuildData guildData = guildMap.get(info.guildId);
            if(guildData == null){
                continue;
            }
            Guild guild = guildData.getGuild();
            if(guild == null){
                continue;
            }
            rank++;
            HumanData humanData = rankHumanDataMap.get(guild.getLeaderId());
            if(humanData != null && defaultHumanData == null){
                defaultHumanData = humanData;
            }
            if(humanData == null){
                humanData = defaultHumanData;
            }
            Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guild, guildData.getSumCombat(), rank, serverId, humanData);
            rankData.addRankInfo(dInfo);
            if(myGuildId == guild.getId()){
                isMyguild = false;
                rankData.setMyRankInfo(dInfo);
            }
        }
        if(isMyguild){
            GuildData guildData = guildMap.get(myGuildId);
            if(guildData != null){
                HumanData humanData = rankHumanDataMap.get(guildData.getGuild().getLeaderId());
                if(humanData == null){
                    humanData = defaultHumanData;
                }
                rankData.setMyRankInfo(RankManager.inst().to_p_rank_info(guildData.getGuild(), guildData.getSumCombat(), rank, serverId, humanData));
            } else {
                rankData.setMyRankInfo(RankManager.inst().to_p_rank_info(null, 0, rank, serverId, defaultHumanData));
            }
        }
    }


    private int retRank(GuildMember a, GuildMember b, int rankIndex){
        int ret = 0;// 0默认相等
        if (a != null && b != null) {
            if(rankIndex == GuildParamKey.rankRule_1){
                // 倒序 在线>离线
                if (a.getOnline() < b.getOnline()) {
                    ret = 1;
                } else if (a.getOnline() > b.getOnline()) {
                    ret = -1;
                }
            } else if(rankIndex == GuildParamKey.rankRule_2) {
                // 倒序 离线时长从小到大排序
                if (a.getOfflineTime() < b.getOfflineTime()) {
                    ret = 1;
                } else if (a.getOfflineTime() > b.getOfflineTime()) {
                    ret = -1;
                }
            } else if(rankIndex == GuildParamKey.rankRule_3) {
                // 倒序 周活跃>职位高低
                if (a.getDonateWeek() < b.getDonateWeek()) {
                    ret = 1;
                } else if (a.getDonateWeek() > b.getDonateWeek()) {
                    ret = -1;
                }
            }else if(rankIndex == GuildParamKey.rankRule_4) {
                // 正序 职位越小职位越高，
                if (a.getCareer() < b.getCareer()) {
                    ret = -1;
                } else if (a.getDonateWeek() > b.getDonateWeek()) {
                    ret = 1;
                }
            }
        }
        return ret;
    }

    @DistrMethod
    public void createGuildHelp(Param param){
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        String name = Utils.getParamValue(param, "name", "");
        long guildId = Utils.getParamValue(param, "guildId", 0L);
        String head = Utils.getParamValue(param, "head", "");
        int type = Utils.getParamValue(param, "type", 0);
        int subType = Utils.getParamValue(param, "subType", 0);
        long endTime = Utils.getParamValue(param, "endTime", 0L);

        GuildData guildData = guildMap.get(guildId);
        // 检测是否已有帮助
        boolean hasSameHelp = false;
        Map<Integer, List<Long>> typeMap = guildData.getHumanIdTypeHelpIdMap().getOrDefault(humanId, new HashMap<>());
        if (type == GuildParamKey.GuildHelpType_FarmBuilding) {
            List<Long> helpIdList = typeMap.getOrDefault(type, new ArrayList<>());
            for (Long id : helpIdList) {
                GuildHelp help = guildData.getGuildHelp(id);
                if (help == null && help.getSubType() == subType) {
                    hasSameHelp = true;
                    break;
                }
            }
        } else {
            if (typeMap.containsKey(type)) {
                hasSameHelp = true;
            }
        }

        // 没有就构建
        if (!hasSameHelp) {
            GuildHelp help = new GuildHelp();
            help.setId(Port.applyId());
            help.setName(name);
            help.setGuildId(guildId);
            help.setRoleHead(head);
            help.setHumanId(humanId);
            help.setType(type);
            help.setSubType(subType);
            help.setEndTime(endTime);
            help.persist();
            guildData.loadGuildHelp(help);
            guildData.sendHelpToOnlineMember(help);
        }

        // 返回该玩家所有的帮助
        MsgGuild.guild_help_status_s2c msg = guildData.to_guild_help_status_s2c(humanId);
        port.returns("result", new ReasonResult(!hasSameHelp), "statusMsg", msg);
    }

    /**
     *
     * @param guildId
     * @param type          这个类型不是升级的类型，应该说是请求类型
     */
    @DistrMethod
    public void guildHelpList(long guildId, int type) {
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.temp.error("公会不存在，guildId={}", guildId);
            return;
        }
        Map<Long, GuildHelp> guildHelpMap = guildData.getGuildHelpMap();
        List<GuildHelp> helpList = new ArrayList<>();
        for (GuildHelp guildHelp : guildHelpMap.values()) {
            helpList.add(guildHelp);
        }
        Collections.sort(helpList, (h1, h2) -> (int)(h1.getEndTime() - h2.getEndTime()));
        List<Define.p_guild_help> dInfoList = new ArrayList<>();
        for (GuildHelp help : helpList) {
            dInfoList.add(GuildManager.inst().to_p_guild_help(help));
        }

        port.returns("dInfoList", dInfoList);
    }

    @DistrMethod
    public void guildHelp(long humanId, long helpId, long guildId, int dailyNum){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", false);
            return;
        }
        List<GuildHelp> helpList;
        if (helpId == 0) {
            helpList = new ArrayList<>(guildData.getGuildHelpMap().values());
        } else {
            GuildHelp help = guildData.getGuildHelp(helpId);
            if (help == null) {
                port.returns("result", false, "errorSn", 189);
                return;
            }
            helpList = new ArrayList<>();
            helpList.add(help);
        }
        int helpNum = 0;
        for (GuildHelp help : helpList) {
            List<Long> humanIdList = Utils.strToLongList(help.getHelpers());
            if (humanIdList.contains(humanId) || help.getHumanId() == humanId) {
                if (helpId == 0) {
                    continue;
                } else {
                    port.returns("result", false);
                    return;
                }
            }
            if (help.getEndTime() * Time.SEC <= Port.getTime()) {
                if (helpId == 0) {
                    continue;
                } else {
                    port.returns("result", false, "errorSn", 189);
                    return;
                }
            }
            ConfGuildLevel confLv = ConfGuildLevel.get(guildData.getGuild().getLevel());
            if (help.getHelpNum() >= confLv.help_limit) {
                if (helpId == 0) {
                    continue;
                } else {
                    guildData.delFullHelpNumGuildHelp(help);
                    port.returns("result", false);
                    return;
                }
            }

            help.setHelpNum(help.getHelpNum() + 1);
            // 帮助
            humanIdList.add(humanId);
            help.setHelpers(Utils.listToString(humanIdList));

            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.help(help.getHumanId(), help.getType(), help.getSubType());

            // 这里再做判定，如果协助次数满了，就要删掉
            if (help.getHelpNum() >= guildData.getHelpNum()) {
                guildData.delFullHelpNumGuildHelp(help);
            }
            helpNum++;
        }
        port.returns("result", true, "helpNum", helpNum);
    }

    /**
     * 删除已经升级完成的协助，不然玩家下次申请协助会失败，因为重复了
     */
    @DistrMethod
    public void delUpgradeFinishHelp(Param param) {
        long humanId = Utils.getParamValue(param, "humanId", 0L);
        long guildId = Utils.getParamValue(param, "guildId", 0L);
        int type = Utils.getParamValue(param, "type", 0);
        int subType = Utils.getParamValue(param, "subType", 0);

        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }

        Map<Integer, List<Long>> typeHelpMap = guildData.getHumanIdTypeHelpIdMap().getOrDefault(humanId, new HashMap<>());
        List<Long> helpIdList = typeHelpMap.getOrDefault(type, new ArrayList<>());
        List<GuildHelp> helpList = new ArrayList<>();
        if (type == GuildParamKey.GuildHelpType_FarmBuilding) {
            for (Long helpId : helpIdList) {
                GuildHelp help = guildData.getGuildHelp(helpId);
                if (help != null && help.getSubType() == subType) {
                    helpList.add(help);
                }
            }
        } else {
            for (Long helpId : helpIdList) {
                GuildHelp help = guildData.getGuildHelp(helpId);
                if (help != null) {
                    helpList.add(help);
                }
            }
        }

        for (GuildHelp help : helpList) {
            guildData.delFullHelpNumGuildHelp(help);
        }
        // TODO 可能需要给其他成员发协议通知他们更新。ememem，看后面效果
    }

    @DistrMethod
    public void guildBoss(long humanId, long guildId, long hurt, int boxNum, int highBoxNum){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        Guild guild = guildData.getGuild();
        GuildMember guildMember = guildData.getMember(humanId);
        int bossBoxNum = guild.getBossBoxNum() + boxNum;
        if(bossBoxNum > guildData.bossBoxMaxNum){
            bossBoxNum = guildData.bossBoxMaxNum;
        }
        guild.setBossBoxNum(bossBoxNum);
        if(highBoxNum > 0){
            int highNum = guild.getBossHighBoxNum() + highBoxNum;
            if(highNum > guildData.bossBoxMaxNum){
                highNum = guildData.bossBoxMaxNum;
            }
            guild.setBossHighBoxNum(highNum);
        }
        if(hurt > guildMember.getBossMaxHurt()){
            guildMember.setBossMaxHurt(hurt);
            List<String> keyList = new ArrayList<>();
            keyList.add(RedisKeys.guildBossHurt + guildId);
            keyList.add(String.valueOf(hurt));
            keyList.add(String.valueOf(humanId));
            EntityManager.redisClient.zadd(keyList,  r->{
                if(!r.succeeded()){
                    Log.temp.error("===保存失败，keyList={}", keyList);
                }
            });
        }
        guild.update();
    }

    @DistrMethod
    public void guildBossGetReward(long humanId, long guildId, int type, int yetBoxNum){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        Guild guild = guildData.getGuild();
        int bossBoxNum = 0;
        int boxMaxNum = 0;
        if(type == GuildParamKey.soloBossBox){
            boxMaxNum = guild.getBossBoxNum();
            bossBoxNum = guild.getBossBoxNum() - yetBoxNum;
        } else if(type == GuildParamKey.soloBossBoxHigh){
            boxMaxNum = guild.getBossHighBoxNum();
            bossBoxNum = guild.getBossHighBoxNum() - yetBoxNum;
        }
        port.returns("bossBoxNum", bossBoxNum,"boxMaxNum", boxMaxNum, "guildLv", guild.getLevel());
    }

    @DistrMethod
    public void dungeon_league_solo_update_box_s2c(long guildId, long humanId, int boxGotNum, int highBoxGotNum){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("msg", null);
            return;
        }
        Guild guild = guildData.getGuild();
        GuildMember member = guildData.getMember(humanId);

        MsgDungeon.dungeon_league_solo_update_box_s2c.Builder msg = MsgDungeon.dungeon_league_solo_update_box_s2c.newBuilder();
        Define.p_league_solo_box.Builder dInfo = Define.p_league_solo_box.newBuilder();
        dInfo.setType(GuildParamKey.soloBossBox);
        dInfo.setCount(guild.getBossBoxNum());
        dInfo.setGotCount(boxGotNum);
        dInfo.setRareOfferName(member.getName());
        msg.addBoxInfo(dInfo);

        Define.p_league_solo_box.Builder dInfoBoxHigh = Define.p_league_solo_box.newBuilder();
        dInfoBoxHigh.setType(GuildParamKey.soloBossBoxHigh);
        dInfoBoxHigh.setCount(guild.getBossHighBoxNum());
        dInfoBoxHigh.setGotCount(highBoxGotNum);
        dInfoBoxHigh.setRareOfferName(member.getName());
        msg.addBoxInfo(dInfoBoxHigh);
        port.returns("msg", msg.build());
    }

    @DistrMethod
    public void gveSign(long guildId, long humanId) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("code", 0, "num", 0);
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            port.returns("code", 0, "num",  guildData.gveSignList.size());
            return;
        }
        if (guildData.gveSignList.contains(humanId)) {
            if (guildGveReady) {
                List<Long> humanIdList = new ArrayList<>(2);
                humanIdList.add(humanId);
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                prx.eventFire(humanIdList, EventKey.GUILD_GVE_CACHE_HUMANDATA, new Param());
            }
            port.returns("code", 0, "num",  guildData.gveSignList.size());
            return;
        }
        member.setGveAppTime(Port.getTime());
        guildData.gveSignList.add(humanId);
        if (guildGveReady) {
            List<Long> humanIdList = new ArrayList<>(2);
            humanIdList.add(humanId);
            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
            prx.eventFire(humanIdList, EventKey.GUILD_GVE_CACHE_HUMANDATA, new Param());
        }
        port.returns("code", 0, "num",  guildData.gveSignList.size());
    }

    @DistrMethod
    public void guildBossGveInfo(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        Guild guild = guildData.getGuild();
        GuildMember guildMember = guildData.getMember(humanId);
        long timeNow = Port.getTime();
        long timeOpen = GuildManager.inst().getGveTime(true, false);
        long timeEnd = GuildManager.inst().getGveTime(false, false);
        ConfLeagueGveChapter confGve = ConfLeagueGveChapter.get(1);
        int sec = confGve.time;
        long limitTime = ConfGlobal.get(ConfGlobalKey.league_gve_chapter_time_limit.SN).value * Time.SEC;
        int stage = 0;
        int gveBattleState = guildData.getGveBattleState();
        if (timeNow > timeEnd) {
            stage = 0;
        } else if (guildData.gveDelHumanIdList.contains(humanId) || (gveBattleState > 0 && gveBattleState != RunState.Running.getType())) {
            stage = 2;
        } else if (timeNow + limitTime > timeOpen) {
            stage = 1;
        }

        MsgGuildBoss.guild_boss_info_s2c.Builder msg = MsgGuildBoss.guild_boss_info_s2c.newBuilder();
        msg.setStage(stage);
        msg.setEndTime((int)(timeOpen / Time.SEC));
        msg.setLevel(guild.getGveSn());
        msg.setNum(guildData.gveSignList.size());
        int isJoin = 0;
        if (guildData.gveDelHumanIdList.contains(humanId) || (gveBattleState > 0 && gveBattleState != RunState.Running.getType())) {
            isJoin = 2;
        } else if (guildData.gveSignList.contains(humanId)) {
            isJoin = 1;
        }
        msg.setIsJoin(isJoin);
        msg.setIsToday(0);
        msg.setHasJoin(0);
        if(Utils.isSameDay(timeNow, guildMember.getSendGveTime())){
            msg.setIsToday(1);
            msg.setHasJoin(1);
        }
        msg.setInSec(sec);
        port.returns("msg", msg.build());
    }

    @DistrMethod
    public void gveEnd(){
        ttGveEnd.stop();
        guildGveOpen = false;
        guildGveReady = false;

        int i = 0;
        for (GuildData guildData : guildMap.values()) {
            guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_3, 0);
            scheduleOnce(new ScheduleTask() {
                @Override
                public void execute() {
                    guildData.guildGveEnd(gveTodayRewardHumanIdSet);
                }
            }, i * 200L);
            i++;
        }
        initGveTime();
    }



    @DistrMethod
    public void guildGveEnter(long guildId, long humanId, byte[] brief) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("dInfoList", null, "result", new ReasonResult(false, 1));
            return;
        }
        if (!guildData.gveHumanIdMap.containsKey(humanId)) {
            guildData.gveHumanIdMap.put(humanId, new Vector2D(0,0));
        }
        guildData.updateGveBriefCache(humanId, brief);
        if (guildGveOpen) {
            int gvePhase = guildData.addBattleGve(humanId, brief);
            if (gvePhase == 1) {
                port.returns("dInfoList", null, "result", new ReasonResult(false, 508));
                return;
            }
            int gveBattleState = guildData.getGveBattleState();
            if (gveBattleState != RunState.Running.getType() || guildData.isExit) {
                Log.game.error("公会【{}】gveBattleSeed={}，runState={}，isExit={}!", guildData.getGuildName(), guildData.gveBattleSeed, gveBattleState, guildData.isExit);
                port.returns("dInfoList", null,  "result", new ReasonResult(false, 379));
                return;
            }
            if (guildData.gveDelHumanIdList.contains(humanId)) {
                port.returns("dInfoList", null, "result", new ReasonResult(false, 379));
                return;
            }
            long timeNow = Port.getTime();
            long timeEnd = GuildManager.inst().getGveTime(false, false);
            MsgGuildBoss.guild_boss_start_s2c msg = guildData.to_guild_boss_start_s2c(timeNow, timeEnd, humanId);
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendMsg(humanId, msg);
            port.returns("dInfoList", null, "result", new ReasonResult(true));
            return;
        }

        List<Define.p_guild_boss_member> dInfoList = new ArrayList<>();
        for (Long signHumanId : guildData.gveHumanIdMap.keySet()) {
            Define.p_battle_role p_battle_role = guildData.getGveBattleRole(signHumanId);
            if (p_battle_role == null) {
                continue;
            }
            Define.p_guild_boss_member.Builder dMember = Define.p_guild_boss_member.newBuilder();
            dMember.setRoleId(p_battle_role.getId());
            dMember.setRoleName(p_battle_role.getName());
            dMember.setFigure(p_battle_role.getFigure());
            dInfoList.add(dMember.build());
        }
        port.returns("dInfoList", dInfoList, "result", new ReasonResult(true));
    }

    @DistrMethod
    public void guildGveExit(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        if (!guildData.gveHumanIdMap.containsKey(humanId)) {
            return;
        }
        guildData.gveHumanIdMap.remove(humanId);
    }

    @DistrMethod
    public void cacheGveSignHumanBrief(long guildId, long humanId, byte[] battleRole) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            Log.guild.error("家族胖头鱼缓存玩家数据时，家族信息不存在，humanId={}，guildId={}", humanId, guildId);
            return;
        }
        if (!guildData.gveSignList.contains(humanId)) {
            return;
        }
        guildData.updateGveBriefCache(humanId, battleRole);
    }

    private void checkActBox(long nowTime) {
        if (ttBoxOpen.isPeriod(nowTime)) {
            ConfGlobal confBox = ConfGlobal.get(ConfGlobalKey.treasure_hunt_exist_time.SN);
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.treasure_hunt_time.SN);
            int[] intArr = Utils.parseIntArray2(confGlobal.strValue)[0];
            long timeOpen = Utils.getDayTime(nowTime, intArr[0], intArr[1], intArr[2]);
            long timeEnd = timeOpen + confBox.value * Time.SEC * turnSum;
            if (nowTime >= timeEnd) {
                ttBoxOpen.stop();
                ttBoxOpen.start(timeOpen + Time.DAY - nowTime);
                return;
            } else {
                int turn = (int) ((nowTime - timeOpen) / (confBox.value * Time.SEC));
                actBoxStart(timeOpen + confBox.value * Time.SEC * turn, turn + 1);
            }
        }
        if (ttBoxTurn.isPeriod(nowTime)) {
            turnNow++;
            if (turnNow > turnSum) {
                actBoxEnd();
                return;
            }
            checkCreateBox(turnNow, ttBoxTurn.getNextTime());
        }
    }

    private void actBoxStart(long timeStart, int turn) {
        Log.guild.error("家族宝箱活动开启，开启的轮次={}", turn);
        ttBoxOpen.stop();
        ConfGlobal confBox = ConfGlobal.get(ConfGlobalKey.treasure_hunt_exist_time.SN);
        ttBoxTurn.start(timeStart, confBox.value * Time.SEC);
        // 活动开始就决定寻宝sn，防止中途人数变动
        for (GuildData guildData : guildMap.values()) {
            guildData.confTreasureHuntSn = guildData.getMemberList().size();
            guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_1, 1);
        }
        checkCreateBox(turn, ttBoxTurn.getNextTime());
    }

    private void actBoxEnd() {
        turnNow = 0;
        ttBoxTurn.stop();
        for (GuildData guildData : guildMap.values()) {
            guildData.actBoxEnd();
            guildData.sendMsg_guild_schedule_s2c(ParamKey.ActivitySchedule_Sn_1, 0);
        }
    }

    private void checkCreateBox(int turn, long endTime) {
        turnNow = turn;
        for (GuildData guildData : guildMap.values()) {
            createBox(guildData, turn, endTime);
        }
    }

    private void createBox(GuildData guildData, int turn, long turnEndTime) {
        ConfTreasureHunt conf = ConfTreasureHunt.get(guildData.confTreasureHuntSn);
        if (conf == null) {
            Log.temp.error("===ConfTreasureHunt配表错误，sn={}" , guildData.confTreasureHuntSn);
            return;
        }
        if (turn > conf.turn) {
            return;
        }
        guildData.createBox(turn, turnEndTime);
    }

    @DistrMethod
    public void boxOpen(long guildId, long humanId, int boxId, int dailyNum){
        GuildData guildData = guildMap.get(guildId);
        GuildBox box = guildData.guildBoxMap.get(boxId);
        if (box == null) {
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        if (!guildData.canPickBox(dailyNum)) {
            port.returns("result", new ReasonResult(false, ErrorTip.CountNotEnough));
            return;
        }
        if (!box.isOpen(humanId)) {
            port.returns("result", new ReasonResult(false, ErrorTip.RewardHasGet));
            return;
        }
        box.add(humanId);

        List<Long> humanIdList = new ArrayList<>(guildData.areaHumanIdList);
        humanIdList.remove(humanId);
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.sendGuildTreasureBoxInfo(humanIdList);

        port.returns("result", new ReasonResult(true), "rewardOutput", box.getRewardOutput());
    }

    @DistrMethod
    public void treasureBox(long guildId) {
        GuildData guildData = guildMap.get(guildId);
        Map<Integer, GuildBox> guildBoxMap = guildData.guildBoxMap;
        List<Define.p_guild_treasure_box> dInfoList = new ArrayList<>();
        for (GuildBox box : guildBoxMap.values()) {
            dInfoList.add(GuildManager.inst().to_p_guild_treasure_box(box));
        }
        port.returns("dInfoList", dInfoList, "turn", turnNow, "turnEndTime", guildData.turnEndTime, "sn", guildData.confTreasureHuntSn);
    }

    /**
     * 重置趣味竞答
     */
    private void resetActQuestion() {
        ttQuestionReward.stop();
        ttQuestionGetRank.stop();
        long timeNow = Port.getTime();
        int gapTime = ConfGlobal.get(ConfGlobalKey.quiz_gap_time.SN).value;
        int questionNum = ConfGlobal.get(ConfGlobalKey.quiz_question_num.SN).value;
        // 初始化题库
        initQuestionList(questionNum);
        ConfGlobal confStartTime = ConfGlobal.get(ConfGlobalKey.quiz_start_time.SN);
        int[] intArr = Objects.requireNonNull(Utils.parseIntArray2(confStartTime.strValue))[0];
        long timeOpen = Utils.getDayTime(timeNow, intArr[0], intArr[1], intArr[2]);
        long timeEnd = timeOpen + gapTime * Time.SEC * questionNum;
        if (timeNow >= timeEnd) {
            // 当前时间已过活动结束时间，开启明天的结算倒计时
            timeEnd += Time.DAY;
            ttQuestionReward.start(timeEnd - timeNow);
            Log.guild.info("当前时间已过活动结束时间，开启明天的结算倒计时 interval={}", timeEnd - timeNow);
            // 当前时间已过活动结束时间，开启明天的获取排行榜计时器
            timeOpen += Time.DAY;
            ttQuestionGetRank.start(timeOpen, 5*Time.SEC);
            Log.guild.info("当前时间已过活动结束时间，开启明天的获取排行榜计时器 interval={} {}", timeOpen - timeNow, ttQuestionGetRank);
        }
        else{
            ttQuestionReward.start(timeEnd - timeNow);
            Log.guild.info("当前时间小于今日活动开启时间，开启结算倒计时 interval={}", timeEnd - timeNow);
            if (timeNow < timeOpen) {
                // 当前时间小于今日活动开启时间，开启获取排行榜计时器
                ttQuestionGetRank.start(timeOpen, 5*Time.SEC);
                Log.guild.info("当前时间小于今日活动开启时间，开启获取排行榜计时器 interval={} {}", timeOpen - timeNow, ttQuestionGetRank);
            }
            else{
                // 当前时间正在今天的活动时间内，立即开启获取排行榜计时器
                ttQuestionGetRank.start(5*Time.SEC, true);
                Log.guild.info("当前时间正在今天的活动时间内，立即开启获取排行榜计时器 {}", ttQuestionGetRank);
            }
        }
    }

    /**
     * 初始化题库
     * @param questionNum
     */
    public void initQuestionList(int questionNum) {
        ConfQuiz[] quizArray = ConfQuiz.findAll().toArray(new ConfQuiz[0]);
        int[] randomIdxArray = Tool.randomNumberNoRepeat2(0, quizArray.length-1, questionNum);
        for(int idx : randomIdxArray) {
            questionList.add(quizArray[idx]);
        }
        Log.guild.info("randomIdxArray={} questionList={}", randomIdxArray, questionList);
    }

    public ConfQuiz getQuestionBySN(int sn) {
        return questionList.get(sn - 1);
    }

    /** 趣味竞答公会总排行榜 */
    public static String getGuildQuestionRankRedisKey(){
        return RedisKeys.guildQuestionRank;
    }

    /** 趣味竞答公会内玩家排行榜 */
    public static String getGuildQuestionInnerRankRedisKey(long guildId){
        return RedisKeys.guildQuestionInnerRank + guildId;
    }

    /**
     * 趣味竞答活动心跳
     * @param nowTime
     */
    private void pulseActQuestion(long nowTime) {
        for (GuildData guildData : guildMap.values()) {
            guildData.pulseActQuestion(nowTime);
        }
        int questionNum = ConfGlobal.get(ConfGlobalKey.quiz_question_num.SN).value;
        if (ttQuestionGetRank.isPeriod(nowTime, true)) {
            // 获取趣味竞答全公会排行版
            getQuestionRankInfo();
        }
        if (ttQuestionReward.isOnce(nowTime)) {
            // 结算
            actQuestionReward();
            // 重置趣味竞答
            resetActQuestion();
        }
    }

    /**
     * 获取趣味竞答全公会排行榜
     */
    private void getQuestionRankInfo() {
        RedisTools.getRankListByIndex(EntityManager.redisClient, GuildService.getGuildQuestionRankRedisKey(), 0 ,QUESTION_GUILD_RANK_LIST_NUM, true, ret->{
            if(ret.failed()) {
                Log.rank.error("获取趣味竞答公会总榜失败");
                return;
            }
            JsonArray jsonArray = ret.result();
            if(jsonArray.getList() == null){
                return;
            }
            List<GuildQuestionRankInfo> guildQuestionRankInfoListNew = new ArrayList<>();
            for(int i=0; i<jsonArray.getList().size(); i+=2){
                long guildId = Utils.longValue(jsonArray.getList().get(i));
                int score = Utils.intValue(jsonArray.getList().get(i+1));
                int rank = i/2+1;
                GuildData guildData = guildMap.get(guildId);
                if(guildData == null){
                    Log.guild.warn("公会不存在，guildId={}", guildId);
                    continue;
                }
                guildQuestionRankInfoListNew.add(new GuildQuestionRankInfo(guildId, guildData.getGuildName(), score, rank));
            }
            guildQuestionRankInfoList = guildQuestionRankInfoListNew;
//            Log.guild.info("guildQuestionRankInfoList nextTime={}", ttQuestionGetRank.getNextTime());
            // 每5秒获取并广播全公会排行榜
            for (GuildData guildData : guildMap.values()) {
                guildData.broadcast_guild_question_rank_s2c();
            }
        });
    }

    public List<GuildQuestionRankInfo> getGuildQuestionRankInfoList(){
        return guildQuestionRankInfoList;
    }

    /**
     * 趣味竞答结算
     */
    public void actQuestionReward() {
        Log.guild.info("趣味竞答活动结算!!!");
        // 发总排行榜前几名的邮件奖励和公会经验
        for(GuildQuestionRankInfo rankInfo : guildQuestionRankInfoList){
            long guildId = rankInfo.getGuildId();
            GuildData guildData = guildMap.get(guildId);
            if(guildData == null){
                Log.guild.warn("公会不存在，guildId={}", guildId);
                continue;
            }
            int rank = rankInfo.getRank();
            Map<Integer, Integer> rewards = GlobalConfVal.quizRankRewardMap.get(rank);
            int guildExp = GlobalConfVal.quizRankRewardExpMap.get(rank);
            // 增加公会经验，不计入每日上限
            guildData.addExp(guildExp,true);
            Log.guild.info("趣味竞答结算 增加guildId={} exp={}", guildId, guildExp);
            // 发奖励邮件
            JSONObject jo = new JSONObject();
            JSONObject joTemp1 = new JSONObject();
            joTemp1.put(MailManager.MAIL_K_4, rank);
            jo.put(MailManager.MAIL_PARAM_1, joTemp1);
            JSONObject joTemp2 = new JSONObject();
            joTemp2.put(MailManager.MAIL_K_4, guildExp);
            jo.put(MailManager.MAIL_PARAM_2, joTemp2);
            String itemJSON = Utils.mapIntIntToJSON(rewards);
            sendMailToGuild(guildId, false, MailManager.SYS_SENDER, ParamKey.guildQuestionRank_mailSn,"", jo.toJSONString(), itemJSON, new Param(), new Object[]{});
        }
        for (GuildData guildData : guildMap.values()) {
            guildData.actQuestionReward();
        }
    }

    /**
     * 趣味答题获取题目信息
     * @param guildId
     * @param humanId
     */
    @DistrMethod
    public void guildQuestionInfo(long guildId, long humanId) {
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.guild.warn("公会不存在，guildId={}", guildId);
            return;
        }
        guildData.guildQuestionInfo(humanId);
    }

    /**
     * 趣味竞答答题
     * @param guildId
     * @param humanId
     * @param answer
     */
    @DistrMethod
    public void guildAnswerQuestion(long guildId, long humanId, String answer){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.guild.warn("公会不存在，guildId={}", guildId);
            port.returns("result", false);
            return;
        }
        boolean result = guildData.answerQuestion(humanId, answer);
        port.returns("result", result);
    }

    /**
     * 趣味答题获取骰子信息
     * @param guildId
     * @param humanId
     */
    @DistrMethod
    public void guildDiceInfo(long guildId, long humanId) {
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.guild.warn("公会不存在，guildId={}", guildId);
            return;
        }
        guildData.guildDiceInfo(humanId);
    }

    /**
     * 趣味竞答骰子roll点
     * @param guildId
     * @param humanId
     */
    @DistrMethod
    public void guildRollDice(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            Log.guild.warn("公会不存在，guildId={}", guildId);
            port.returns("result", new ReasonResult(false));
            return;
        }
        GuildRollDiceInfo rollDiceInfo = guildData.rollDice(humanId);
        if(rollDiceInfo == null){
            port.returns("result", new ReasonResult(false));
            return;
        }
        port.returns("result", new ReasonResult(true), "points", rollDiceInfo.getPoints(), "rewards", rollDiceInfo.getRewards());
    }

    @DistrMethod
    public void areaEnter(long humanId, long guildId, Param param){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        guildData.addArea(humanId);

        Define.p_guild_area_member dInfo = param.get("dMember");
        if (param.containsKey("isSync")) {
            Define.p_guild_area_member oldInfo = guildData.dMemberMap.get(humanId);
            if (oldInfo != null) {
                dInfo = dInfo.toBuilder().setPos(oldInfo.getPos()).build();
            }
        }
        guildData.dMemberMap.put(humanId, dInfo);

        MsgGuild.guild_area_broadcast_s2c.Builder msg = MsgGuild.guild_area_broadcast_s2c.newBuilder();
        msg.setType(GuildParamKey.broadcast_type_1);
        msg.setActionMember(dInfo);
        msg.addPosList(dInfo.getPos());

        // 排除自己通知当前工会里的人
        List<Long> areaHumanIdList = new ArrayList<>(guildData.areaHumanIdList);
        areaHumanIdList.remove(humanId);
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.sendMsgTo(areaHumanIdList, msg.build());

        MsgGuild.guild_area_enter_s2c.Builder msgEnter = MsgGuild.guild_area_enter_s2c.newBuilder();
        msgEnter.addAllMemberList(guildData.dMemberMap.values());

        proxy.sendMsg(humanId, msgEnter.build());

        if (turnNow > 0) {
            List<Long> humanIdList = new ArrayList<>();
            humanIdList.add(humanId);
            proxy.sendGuildTreasureBoxInfo(humanIdList);
        }
    }

    @DistrMethod
    public void areaExit(long humanId, long guildId, Param param){
        GuildData guildData = guildMap.get(guildId);
        guildData.removeArea(humanId);
        guildData.dMemberMap.remove(humanId);
        Define.p_guild_area_member dInfo = param.get("dMember");
        MsgGuild.guild_area_broadcast_s2c.Builder msg = MsgGuild.guild_area_broadcast_s2c.newBuilder();
        msg.setType(GuildParamKey.broadcast_type_4);
        msg.setActionMember(dInfo);

        // 排除自己通知当前工会里的人
        List<Long> areaHumanIdList = new ArrayList<>(guildData.areaHumanIdList);
        areaHumanIdList.remove(humanId);
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.sendMsgTo(areaHumanIdList, msg.build());

    }

    @DistrMethod
    public void guildSchedule(long guildId){
        GuildData guildData = guildMap.get(guildId);
        List<Define.p_key_value> dList = new ArrayList();
        // TODO
//        HumanManager.inst().to_p_key_value();


        port.returns("dList", dList);
    }


    @DistrMethod
    public void guildHelpStatus(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData== null){
            Log.temp.error("公会不存在，guildId={}", guildId);
            return;
        }
        Map<Long, GuildHelp> helpMap = guildData.getGuildHelpMap();
        List<Define.p_guild_help_status> dInfoList = new ArrayList<>();
        for(GuildHelp help : helpMap.values()){
            if(help.getHumanId() != humanId){
                continue;
            }
            dInfoList.add(GuildManager.inst().to_p_guild_help_status(help));
        }
        port.returns("dInfoList", dInfoList);
    }

    public void autoGuildLeague(){
        if(Utils.isSameWeek2(Util.getOpenServerTime(Config.SERVER_ID), Port.getTime())){
            Log.temp.info("自动公会报名第一周不处理");
            return;
        }

        long stepStartTime = GuildLeagueUtils.getStepStartTime(ParamKey.guild_gvg_step_1);
        long stepEndTime = GuildLeagueUtils.getStepEndTime(ParamKey.guild_gvg_step_1);
        long timwNow = Port.getTime();
        // 整点触发，结束时间延长2分钟
        if (stepStartTime > timwNow || (stepEndTime + 2 * Time.MIN) < timwNow){
//            Log.temp.info("自动公会报名不在报名时间段内。 stepStartTime={} stepEndTime={} nowTime={}", stepStartTime, stepEndTime, Port.getTime());
            return;
        }
        enrollGuildIdSet.clear();
        Log.temp.info("自动公会报名触发开始, {}", guildMap.size());
        List<LeagueMatchGroup> warmUpList = new ArrayList<>();
        Map<Integer, List<LeagueMatchGroup>> serverGuildDataMap = new HashMap<>();
        boolean bridge = GuildLeagueUtils.isBridge(Config.SERVER_ID);
        for(GuildData guildData : guildMap.values()){
            try{
                if(enrollGuildIdSet.contains(guildData.getId())){
                    continue;
                }
                if(guildData.getMemberNum() < GlobalConfVal.guildFamilySignupNum){
                    continue;
                }
                LeagueMatchGroup group = new LeagueMatchGroup();
                group.guildId = guildData.getId();
                group.serverId = guildData.getServerId();
                group.name = guildData.getGuildName();
                group.combat = guildData.getSumCombat();
                group.grade = guildData.getGuild().getGrade();
                group.leaderId = guildData.getGuild().getLeaderId();

                GuildMember member = guildData.getMember(group.leaderId);
                if(member != null){
                    group.leaderName = member.getName();
                }
                group.flagJSON = guildData.getGuild().getFlagJSON();

                enrollGuildIdSet.add(guildData.getId());
                if(Config.DEBUG_PATTERN && !GuildLeagueUtils.isBridge(guildData.getServerId())){// 内网本服报名
                    warmUpList.add(group);
                } else if(!bridge) {// 本服报名
                    warmUpList.add(group);
                } else {
                    List<LeagueMatchGroup> guildDataList = serverGuildDataMap.computeIfAbsent(guildData.getServerId(), k -> new ArrayList<>());
                    guildDataList.add(group);
                }
            }catch (Exception e){
                Log.temp.error("获取公会leaderName失败，guildId={}", guildData.getId(), e);
            }
        }

        if (warmUpList.size() > 0){
            GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
            proxy.guildBattleRegister(warmUpList);
            Log.temp.info("自动公会报名，公会数量：{} 本次本服报名数量：{}", guildMap.size(), warmUpList.size());
        }

        if (serverGuildDataMap.size() > 0){
            try {
                for (Map.Entry<Integer, List<LeagueMatchGroup>> entry : serverGuildDataMap.entrySet()) {
                    int serverId = entry.getKey();
                    List<LeagueMatchGroup> dataList = entry.getValue();
                    CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, serverId, res -> {
                        try {
                            if(res.failed()){
                                Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                                return;
                            }
                            CrossPoint result = res.result();
                            GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                            proxy.guildBattleRegister(dataList);
                            Log.temp.info("自动公会报名，serverId={},公会数量：{} 本次报名数量：{}", serverId, guildMap.size(), dataList.size());
                        } catch (Exception e){
                            Log.temp.error("==跨服链接出问题 ", e);
                        }
                    });
                }
            } catch (Exception e){
                Log.temp.error("====跨服获取数据出问题", e);
            }
        }
        Log.temp.info("本次自动公会报名触发结束");
    }

//    public void checkAutoGuildLeague(){
//        long timeNow = Port.getTime();
//        long timeOpen = Utils.getTimeOfWeek(timeNow, 1, 0);
//        long timeEnd = Utils.getTimeOfWeek(timeNow, 2, 0);
//        if(timeNow < timeOpen || timeNow > timeEnd){
//            return;
//        }
//        for(GuildData guildData : guildMap.values()){
//            if(enrollGuildIdSet.contains(guildData.getId())){
//                continue;
//            }
//            autoGuildLeague(guildData);
//        }
//    }


    @DistrMethod
    public void guildRank(long guildId, int rankType, long humanId, int page){
        GuildData guildData = guildMap.get(guildId);
        List<Define.p_guild_rank_info> dInfoList = new ArrayList<>();
        MsgGuild.guild_rank_info_s2c.Builder msg = MsgGuild.guild_rank_info_s2c.newBuilder();
        msg.setRankType(rankType);
        msg.setPage(page);
        final int rank = (page - 1) * GuildParamKey.pageNum;
        int endRank = page * GuildParamKey.pageNum;
        int maxPage = 1;
        final String redisKey;
        int maxNum = 0;
        if (rankType == GuildParamKey.rankTypeBoss) {
            redisKey = RedisKeys.guildBossHurt + guildId;
            maxNum = Utils.getRankSumNum(redisKey);
        } else if (rankType == GuildParamKey.rankTypeGve) {
            redisKey = RedisKeys.guildGveHurt + guildId + guildData.getGuild().getGveKey();
            maxNum = Utils.getRankSumNum(redisKey);
            msg.addExt(Define.p_key_value.newBuilder().setK(1).setV((guildData.getGuild().getGveLastTime() / Time.SEC)));
            msg.addExt(Define.p_key_value.newBuilder().setK(2).setV(guildData.getGuild().getGveLastSn()));
        } else {
            redisKey = "";
        }
        if (endRank > maxNum) {
            endRank = maxNum;
        }
        maxPage = (int)Math.ceil(maxNum * 1.0D / GuildParamKey.pageNum);
        msg.setMaxPage(maxPage);
        long pid = port.createReturnAsync();
        RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, rank, endRank - 1, true, ret -> {
            if (ret.failed()) {
                Log.temp.error("获取工会boss排行榜失败，guildId={}", guildId);
                port.returnsAsync(pid, "msg", null);
                return;
            }
            JsonArray jarry = ret.result();
            List<Long> humanIdList = new ArrayList<>();
            int size = jarry.getList().size();
            for (int i = 0; i < size - 1; i += 2) {
                humanIdList.add(Utils.longValue(jarry.getString(i)));
            }
            HumanData.getList(humanIdList, HumanManager.inst().humanClasses2, res -> {
                if (!res.succeeded()) {
                    Log.human.error("MemberCallback getMemberAsync error", res.cause());
                    port.returnsAsync(pid, "msg", null);
                    return;
                }
                List<HumanData> humanDataList = res.result();
                Map<Long, HumanData> idDataMap = new HashMap<>();
                for (HumanData humanData : humanDataList) {
                    idDataMap.put(humanData.human.getId(), humanData);
                }
                humanDataList.clear();
                int rankIndex = rank;
                Define.p_guild_rank_info myRank = null;
                for (int i = 0; i < size - 1; i += 2) {
                    ++rankIndex;
                    long memberId = humanIdList.get(i / 2);
                    long hurt = Utils.longValue(jarry.getString(i + 1));
                    HumanData data = idDataMap.get(memberId);
                    Define.p_guild_rank_info.Builder dInfo = GuildManager.inst().to_p_guild_rank_info(data.human, data.human2, rankIndex, hurt);
                    if (humanId == memberId) {
                        myRank = dInfo.build();
                    }
                    dInfoList.add(dInfo.build());
                }
                idDataMap.clear();
                msg.addAllRankList(dInfoList);
                msg.setGuildId(guildId);
                if (myRank == null) {
                    String humanIdStr = String.valueOf(humanId);
                    List<Request> requestList = new ArrayList<>();
                    requestList.add(Request.cmd(Command.ZREVRANK, redisKey, humanIdStr));
                    requestList.add(Request.cmd(Command.ZSCORE, redisKey, humanIdStr));
                    RedisTools.batch(EntityManager.redis, requestList, listRes -> {
                        if (!listRes.succeeded()) {
                            Log.temp.error("获取玩家排行信息错误，redisKey={}，humanId={}", redisKey, humanIdStr);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        long myRankIndex = listRes.result().get(0);
                        long myRankScore = listRes.result().get(1);
                        myRankScore = myRankScore == -1 ? 0 : myRankScore;
                        port.returnsAsync(pid, "msg", msg.build(), "containMyRank", false, "myRankIndex", myRankIndex + 1, "myRankScore", myRankScore);
                    }, (index, response) -> response == null ? -1L : Utils.longValue(response));
                    return;
                }
                msg.setMyRank(myRank);
                port.returnsAsync(pid, "msg", msg.build(), "containMyRank", true);
            });
        });
    }

    private Define.p_guild_rank_info getMyRank(GuildData guildData, List<RankInfo> rankList, long humanId){
        Define.p_guild_rank_info myRank = null;
        int rank = 0;
        for(RankInfo info : rankList){
            GuildMember member = guildData.getMember(info.humanId);
            if(member == null){
                continue;
            }
            ++rank;
            if(info.humanId != humanId){
                continue;
            }
            myRank = info.to_p_guild_rank_info(rank, member);
            break;
        }
        return myRank;
    }

    @DistrMethod
    public void guildRankMyInfo(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("myRank", null);
            return;
        }
        long hurt = Utils.longValue(Utils.getRedisScore(RedisKeys.guildBossHurt + guildId, String.valueOf(humanId)));
        int rank = Utils.intValue(Utils.getRedisRank(RedisKeys.guildBossHurt + guildId, String.valueOf(humanId)));
        GuildMember member = guildData.getMember(humanId);
        if(member != null){
            RankInfo info = new RankInfo();
            info.humanId = humanId;
            info.param = hurt;
            info.serverId= guildData.getServerId();
            port.returns("myRank", info.to_p_guild_rank_info(rank, member));
        } else {
            HumanData.getHumanDataAsync(humanId, new Class<?>[]{Human.class, Human2.class}, res -> {
                if (res.failed()) {
                    Log.temp.error("获取人物数据失败，humanId={}", humanId);
                    port.returns("myRank", null);
                } else {
                    HumanData humanData =  (HumanData)res.result();
                    RankInfo info = new RankInfo(humanData.human, humanData.human2, hurt);
                    port.returns("myRank", info.to_p_guild_rank_info(rank,humanData.human));
                }

            });
        }
    }




    @DistrMethod
    public void dungeonBattle(long guildId, int type, long humanId, Param param){
        List<Define.p_key_value> argsList  = param.get("argsList");
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            port.returns("result", false ,"hurt", 0, "maxHurt", 0);
            return;
        }
        long hurt = 0;
        int bossLv = 1;
        for (Define.p_key_value args : argsList) {
            if (args.getK() == GuildParamKey.solo_args_k_1) {
                bossLv = Utils.intValue(args.getV());
            }
            if (args.getK() == GuildParamKey.solo_args_k_2) {
                hurt = args.getV();
            }
        }
        Guild guild = guildData.getGuild();
        int guildLv = guild.getLevel();
        GuildMember member = guildData.getMember(humanId);
        if (type == InstanceConstants.LEAGUESOLOCHAPTER_6) {// GuildParamKey.rankTypeBoss
            if (member.getBossMaxHurt() < hurt) {
                member.setBossMaxHurt(hurt);
                member.update();

                List<String> keyList = new ArrayList<>();
                keyList.add(RedisKeys.guildBossHurt + guildId);
                keyList.add(String.valueOf(hurt));
                keyList.add(String.valueOf(humanId));
                EntityManager.redisClient.zadd(keyList,  r->{
                    if(!r.succeeded()){
                        Log.temp.error("===保存失败，keyList={}", keyList);
                    }
                });
            }
        }

        int normalBoxNum = GuildManager.inst().getNormalBoxNum(guildLv, bossLv);
        int highBoxNum = GuildManager.inst().getHighBoxNum(guildLv, bossLv);
        ConfLeagueSoloChapterChest conf = ConfLeagueSoloChapterChest.get(guildLv);
        if (conf == null) {
            Log.temp.error("ConfLeagueSoloChapterChest配表错误， guildLv={}, bossLv={}", guildLv, bossLv);
            port.returns("result", true ,"hurt", hurt, "maxHurt", member.getBossMaxHurt());
            return;
        }
        List<String> keyList = new ArrayList<>();
        keyList.add(RedisKeys.guildBoxRank + guildId);
        JSONObject jo = new JSONObject();
        jo.put("id", humanId);
        jo.put("name", member.getName());
        jo.put("hurt", hurt);
        if (normalBoxNum > 0) {
            jo.put("type1", GuildParamKey.soloBossBox);
            jo.put("num1", normalBoxNum);
        }
        if (highBoxNum > 0) {
            jo.put("type2", GuildParamKey.soloBossBoxHigh);
            jo.put("num2", highBoxNum);
        }
        if (normalBoxNum > 0 || highBoxNum > 0) {
            guildData.guildList.add(jo);
            keyList.add(String.valueOf(Utils.getTimeSec()));
            keyList.add(jo.toJSONString());
            EntityManager.redisClient.zadd(keyList, r->{
                if(!r.succeeded()){
                    Log.temp.error("保存失败, keyList={}", keyList);
                }
            });
        }

        int dailyBoxNum = guild.getBossBoxNum() + normalBoxNum;
        int dailyHighBoxNum = guild.getBossHighBoxNum() + highBoxNum;
        if (dailyBoxNum >= conf.normal_chest_limit) {
            dailyBoxNum = conf.normal_chest_limit;
        }
        if (dailyHighBoxNum >= conf.rare_chest_limit) {
            dailyHighBoxNum = conf.rare_chest_limit;
        }
        guild.setBossBoxNum(dailyBoxNum);
        guild.setBossHighBoxNum(dailyHighBoxNum);
        guild.update();
        dungeon_league_solo_info_s2c(guildId, true);
        port.returns("result", true ,"hurt", hurt, "maxHurt", member.getBossMaxHurt());
    }

    @DistrMethod
    public void dungeon_league_solo_info_s2c(long guildId, boolean isAll){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        Guild guild = guildData.getGuild();
        if (guild == null) {
            return;
        }
        List< Define.p_league_solo_box_record> dInfoList = new ArrayList<>();

        for (JSONObject jo : guildData.guildList) {
            if (jo.isEmpty()) {
                continue;
            }
            Define.p_league_solo_box_record.Builder dInfo = Define.p_league_solo_box_record.newBuilder();
            long humanId = jo.getLongValue("id");
            GuildMember guildMember = guildData.getMember(humanId);
            String name = guildMember == null ? jo.getString("name") : guildMember.getName();
            dInfo.setRoleId(humanId);
            dInfo.setName(name);
            dInfo.setHurt(Utils.getNumString(Utils.floatValue(jo.getLongValue("hurt"))));
            if (jo.containsKey("num1")) {
                dInfo.addBoxInfo(HumanManager.inst().to_p_key_value(GuildParamKey.soloBossBox, jo.getIntValue("num1")));
            }
            if (jo.containsKey("num2")) {
                dInfo.addBoxInfo(HumanManager.inst().to_p_key_value(GuildParamKey.soloBossBoxHigh, jo.getIntValue("num2")));
            }
            dInfoList.add(dInfo.build());
        }

        Param param = new Param();
        param.put("dInfoList", dInfoList);
        param.put("boxNum", guild.getBossBoxNum());
        param.put("boxHighNum", guild.getBossHighBoxNum());
        param.put("msgId", MsgIds.dungeon_league_solo_info_s2c);

        if (isAll) {
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendMsgParam(guildData.areaHumanIdList, param);
        } else {
            port.returns("boxNum", guild.getBossBoxNum(), "boxHighNum", guild.getBossHighBoxNum(), "dInfoList", dInfoList);
        }
    }

    @DistrMethod
    public void sendMailToGuild(long receiverId, boolean skipLeader, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object[] contentParam) {
        GuildData guildData = guildMap.get(receiverId);
        if(guildData == null){
            return;
        }
        long leaderId = guildData.getGuild().getLeaderId();
        for(GuildMember member : guildData.getMemberList()){
            if(skipLeader && member.getHumanId() == leaderId){
                continue;
            }
            MailManager.inst().sendMail(member.getHumanId(), senderId, mailSn, title, content, itemJSON, params, contentParam);
        }
    }


    @DistrMethod
    public void sendMailToGuildLeader(long receiverId, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object[] contentParam) {
        GuildData guildData = guildMap.get(receiverId);
        if(guildData == null){
            return;
        }
        long leaderId = guildData.getGuild().getLeaderId();
        MailManager.inst().sendMail(leaderId, senderId, mailSn, title, content, itemJSON, params, contentParam);
    }


    @DistrMethod
    public void gm(int type, String json){
        if(type == 1){
            int needMemberNum = Utils.intValue(json);
            for(GuildData guildData : guildMap.values()){
                if(guildData.getMemberNum() < needMemberNum){
                    // 小于公会需求成员数则跳过
                    continue;
                }
                LeagueMatchGroup group = new LeagueMatchGroup();
                group.guildId = guildData.getId();
                group.serverId = guildData.getServerId();
                group.name = guildData.getGuildName();
                group.combat = guildData.getSumCombat();
                group.grade = guildData.getGuild().getGrade();
                group.leaderId = guildData.getGuild().getLeaderId();
                group.flagJSON = guildData.getGuild().getFlagJSON();
                try{
                    group.leaderName = guildData.getMember(group.leaderId).getName();
                }catch(Exception e) {
                    Log.temp.error("==获取队长名字出错, guildId={}, leaderId={}", group.guildId, group.guildId, e);
                }

                GuildLeagueWarmUpServiceProxy proxy2 = GuildLeagueWarmUpServiceProxy.newInstance();
                proxy2.guildBattleRegister(Arrays.asList(group));

                try {
                    CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, guildData.getServerId(), res -> {
                        try {
                            if(res.failed()){
                                Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                                return;
                            }
                            CrossPoint result = res.result();
                            Log.temp.info("== CrossPoint={}", result);
                            GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                            proxy.guildBattleRegister(Arrays.asList(group));
                        } catch (Exception e){
                            Log.temp.error("==跨服链接出问题 ", e);
                        }
                    });
                } catch (Exception e){
                    Log.temp.error("====跨服获取数据出问题", e);
                }
            }

        } else if(type == 2){
            guildAutoJoin();
            for(GuildData guildData : guildMap.values()){
                if(guildData == null){
                    continue;
                }
                long guildId = guildData.getGuild().getId();
//                long openServerTime = Util.getOpenServerTime(guildData.getServerId());
//                int week = Utils.getDayOfWeek(openServerTime);
//                ConfActivityAdjust conf = ConfActivityAdjust.get(20, week);
//                long timeOpenWarmUp = Utils.getOffDayTime(openServerTime, conf.day[0][0], 0);
//                long timeEndWarmUp = Utils.getOffDayTime(openServerTime, conf.day[0][1], ParamKey.guildLeagueEnd22);
                List<Long> humanIdList = guildData.getHumanIdList();

                GuildLeagueWarmUpServiceProxy proxy2 = GuildLeagueWarmUpServiceProxy.newInstance();
                proxy2.autoJoinHumanIdList(guildId, humanIdList);

                try {
                    CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, guildData.getServerId(), res -> {
                        try {
                            if(res.failed()){
                                Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                                return;
                            }
                            CrossPoint result = res.result();
                            GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                            proxy.autoJoinHumanIdList(guildId, humanIdList);
                        } catch (Exception e){
                            Log.temp.error("==跨服链接出问题 ", e);
                        }
                    });
                } catch (Exception e){
                    Log.temp.error("====跨服获取数据出问题", e);
                }
            }
        } else if(type == 3){
            gveOpen();
        } else if(type == 4){
            long timeOpen = GuildManager.inst().getGveTime(true, false);
            long timeEnd = GuildManager.inst().getGveTime(false, false);
            Log.temp.error("===Gm gve open time, timeOpen={}, timeEnd={}", timeOpen, timeEnd,
                    Utils.formatTime(timeOpen, "yyyy-MM-dd HH:mm:ss"),
                    Utils.formatTime(timeEnd, "yyyy-MM-dd HH:mm:ss"));
            Log.temp.info("=====Gm gve开始时间={}，{}", timeOpen, Utils.formatTime(timeOpen, "yyyy-MM-dd HH:mm:ss"));

            long timeNow = Port.getTime();
            ConfLeagueGveChapter confGve = ConfLeagueGveChapter.get(1);
            int sec = confGve.time;
            long limitTime = ConfGlobal.get(ConfGlobalKey.league_gve_chapter_time_limit.SN).value * Time.SEC;
            int stage = 0;// 未开启
            if(timeNow + limitTime > timeOpen){
                stage= 1;// 可以进入
            }
            if(timeNow > timeEnd){
                stage = 0;
            }

            MsgGuildBoss.guild_boss_info_s2c.Builder msg = MsgGuildBoss.guild_boss_info_s2c.newBuilder();
            msg.setStage(stage);
            msg.setEndTime((int)(timeOpen / Time.SEC));
            msg.setIsToday(0);
            msg.setHasJoin(0);
            msg.setInSec(sec);
            Log.temp.error("=====Gm ==msg={}", msg.build());
        } else if(type == 11){
            GuildData guildData = guildMap.get(600140000060101479L);
            guildData.getGuild().setExp(guildData.getGuild().getExp() + 1);
        } else if(type == 12){
            autoGuildLeague();
        } else if(type == 13){
            JSONObject jo = Utils.toJSONObject(json);
            long guildId = jo.getLongValue("guildId");
            int guildLv = jo.getIntValue("guildLv");
            GuildData guild = guildMap.get(guildId);
            guild.getGuild().setLevel(guildLv);
            guild.getGuild().setExp(0);
        } else if(type == 14){
            JSONObject jo = Utils.toJSONObject(json);
            long guildId = jo.getLongValue("guildId");
            int num = jo.getIntValue("num");
            GuildData guild = guildMap.get(guildId);
            guild.getGuild().setFreeGuildNameNum(num);
        }
    }

    @DistrMethod
    public void getElem(long guildId, long humanId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("elem", "");
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if(member == null){
            port.returns("elem", "");
            return;
        }
        JSONArray ja = new JSONArray();
        JSONObject jo = new JSONObject();
        jo.put("id", ParamKey.elem_id_1);
        jo.put("num", guildData.getPosition(humanId));
        ja.add(jo);

        port.returns("elem", ja.toJSONString());
    }

    private TickTimer guildHelpDelTimer = new TickTimer(30 * Time.SEC);

    @ScheduleMethod(DataResetService.CRON_EVERY_SECOUND)
    public void everySecoundHandle() {
        long currTime = Port.getTime();
        deleteGuildHelp(currTime);
    }

    /**
     * 删除过期的协助
     */
    private void deleteGuildHelp(long currTime) {
        if (!guildHelpDelTimer.isPeriod(currTime)) {
            return;
        }
        for (GuildData guildData : guildMap.values()) {
            guildData.delEndGuildHelp(currTime);
        }
    }


//    private void initAllGuild() {
//        String whereSql = Utils.createStr("select * from {}", Guild.tableName);
//        RowSet<Row> result = EntityManager.executeSql(whereSql);
//        Map<Integer, Integer> serverNumMap = new HashMap<>();
//        JsonArray array = MysqlTool.getRowsArray(result);
//        Iterator itrs = array.stream().iterator();
//        List<String> list = new ArrayList<>();
//        while(itrs.hasNext()) {
//            Guild guild = Utils.toJSONObject(String.valueOf(itrs.next())).toJavaObject(Guild.class);
//            if(guild == null){
//                continue;
//            }
//            Map<Long, Guild> guildMap = serverIdGuildMapAll.get(guild.getGameServerId());
//            if(guildMap == null){
//                guildMap = new HashMap<>();
//                serverIdGuildMapAll.put(Utils.intValue(guild.getGameServerId()), guildMap);
//            }
//            guildMap.put(guild.getId(), guild);
//        }
//
//    }

    @DistrMethod
    public void guild_area_move(long guildId, long humanId, Param param){
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            return;
        }
        Define.p_guild_area_member dInfo = param.get("dMember");
        List<Define.p_pos> posList = param.get("posList");

        guildData.dMemberMap.put(humanId, dInfo);

        MsgGuild.guild_area_broadcast_s2c.Builder msg = MsgGuild.guild_area_broadcast_s2c.newBuilder();
        msg.setType(GuildParamKey.broadcast_type_3);
        msg.setActionMember(dInfo);
        msg.addAllPosList(posList);

        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.sendMsgTo(guildData.areaHumanIdList, msg.build());
    }

    /**
     * 玩家更新名称后，同时更新对应宗门成员的名称，目的是为了日志取的时候取到最新名称
     */
    @DistrMethod
    public void updateGuildMemberName(long guildId, long humanId, String name)  {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            return;
        }
        member.setName(name);
        member.update();
    }

    /**
     * 玩家更新头像，家族成员也相对应的更新
     */
    @DistrMethod
    public void updateGuildMemberHead(long guildId, long humanId, String head) {
        GuildData guildData = guildMap.get(guildId);
        if (guildData == null) {
            return;
        }
        GuildMember member = guildData.getMember(humanId);
        if (member == null) {
            return;
        }
        member.setRoleHead(head);
    }

    @DistrMethod
    public void gmAutoGuildLeague(){
        enrollGuildIdSet.clear();
        this.autoGuildLeague();
    }

    @DistrMethod
    public void gmGveAutoSign(int signNum) {
        for (GuildData data : guildMap.values()) {
            data.gmRandomGveSign(signNum);
        }
    }

    @DistrMethod
    public void update(String json){

    }

    @DistrMethod
    public void update1(Object... objs){
        int type = Utils.intValue(objs[0]);
        if(type == 1){
            for(GuildData guildData : guildMap.values()){
                LeagueMatchGroup group = new LeagueMatchGroup();
                group.guildId = guildData.getId();
                group.serverId = guildData.getServerId();
                group.name = guildData.getGuildName();
                group.combat = guildData.getSumCombat();
                group.grade = guildData.getGuild().getGrade();
                group.leaderId = guildData.getGuild().getLeaderId();
                group.leaderName = guildData.getMember(group.leaderId).getName();
                group.flagJSON = guildData.getGuild().getFlagJSON();

                long openServerTime = Util.getOpenServerTime(guildData.getServerId());
                int week = Utils.getDayOfWeek(openServerTime);
                ConfActivityAdjust conf = ConfActivityAdjust.get(20, week);
                long timeOpenWarmUp = Utils.getOffDayTime(openServerTime, conf.day[0][0], 0);
                long timeEndWarmUp = Utils.getOffDayTime(openServerTime, conf.day[0][1], ParamKey.guildLeagueEnd22);

//                if(Utils.isDebugMode()){// 本服报名
//                    GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
//                    proxy.guildBattleRegister(group);
//                } else {
//                   GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.bridgeLeague(NodeAdapter.bridge(true));
//                    proxy.guildBattleRegister(group);
//                }
//               GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.bridgeLeague(NodeAdapter.bridge(true));
//                proxy.guildBattleRegister(group);
            }
        } else if(type == 2){
            int serverId = Utils.intValue(objs[1]);
            if(serverId == 0 || Utils.isDebugMode()){
                serverId = 300571;
            }
            AllotServerServiceProxy proxy = AllotServerServiceProxy.newInstance(Distr.NODE_ADMIN_SERVER);
            proxy.gm(1);
        } else if(type == 3){
            int serverId = Utils.intValue(objs[1]);
            if(serverId == 0 || Utils.isDebugMode()){
                serverId = 300571;
            }
            AllotServerServiceProxy proxy = AllotServerServiceProxy.newInstance(Distr.NODE_ADMIN_SERVER);
            proxy.gm(2);
        } else if(type == 4){
            for(GuildData guildData : guildMap.values()){
                ConfGuildLevel confGuildLv = ConfGuildLevel.get(guildData.getGuild().getLevel());
                guildData.getGuild().setDailyExp(confGuildLv.max_exp);
                guildData.getGuild().update();
            }
        }
    }

    @DistrMethod
    public void getRoleOthersGuildInfo(long guildId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("memberNum", 0, "guildLv", 0, "name", "", "flagJSON", "");
            return;
        }
        int memberNum = guildData.getMemberList().size();
        Guild guild = guildData.getGuild();
        int guildLv = guild.getLevel();
        String guildName = guild.getName();
        port.returns("memberNum", memberNum, "guildLv", guildLv, "name", guildName, "flagJSON", guildData.getGuild().getFlagJSON());
    }

    @DistrMethod
    public void update2(Param param){

    }

    @DistrMethod
    public void update3(String json){

    }

    @DistrMethod
    public void getGuildRankInfo(List<RankInfo> rankInfoList, long guildId, long humanId, int type){
        MsgGvg.gvg_week_rank_s2c.Builder msg = MsgGvg.gvg_week_rank_s2c.newBuilder();
        msg.setType(type);
        long pid = port.createReturnAsync();
        List<Long> humanIdList = new ArrayList<>();
        for(RankInfo rankInfo : rankInfoList){
            GuildData guildData = guildMap.get(rankInfo.humanId);
            if(guildData == null){
                continue;
            }
            humanIdList.add(guildData.getGuild().getLeaderId());
        }

       HumanData.getList(humanIdList, HumanManager.humanClasses2, ret -> {
           if(ret.failed()){
               port.returns(pid, ret.cause());
               return;
           }
           List<HumanData> humanDataList = ret.result();
           Map<Long, HumanData> humanDataMap = new HashMap<>();
           for(HumanData humanData : humanDataList){
               humanDataMap.put(humanData.human.getId(), humanData);
           }
           int myScore = 0;
           for(RankInfo rankInfo : rankInfoList) {
               GuildData guildData = guildMap.get(rankInfo.humanId);
               if(guildData == null){
                   continue;
               }
               Guild guild = guildData.getGuild();
               if(guild == null){
                   continue;
               }

               Define.p_rank_info dInfoRank = RankManager.inst().to_p_rank_info(guild, rankInfo.param, rankInfo.rank,
                       guild.getGameServerId(), humanDataMap.get(guild.getLeaderId()));
               msg.addRankInfo(dInfoRank);
               if(guild.getId() == guildId){
                   msg.setMyRankInfo(dInfoRank);
                   myScore = Utils.intValue(rankInfo.param);
               }
           }
           port.returnsAsync(pid, "msg",msg.build(), "guild", guildMap.get(guildId).getGuild(), "score", myScore);
       });
    }

    public void guildRankList(int serverId, int page, long myGuildId, int rankSn){
        ConfRanktype confRanktype = ConfRanktype.get(rankSn);
        if(confRanktype == null){
            return;
        }
        String redisKey = RankManager.inst().getRedisRankTypeKey(serverId, confRanktype);

        long pid = port.createReturnAsync();
        MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();

        RedisTools.getRankLen(EntityManager.redisClient, redisKey, rest -> {
            if (rest.failed()) {
                Log.friend.error("获取失败，rankSn={}", rankSn);
                return;
            }
            long total = rest.result();
            int openIndex = (page - 1) * RankParamKey.pageNum;
            int endIndex = page * RankParamKey.pageNum - 1;
            openIndex = Math.min(openIndex, confRanktype.show_num);
            endIndex = Math.min(endIndex, confRanktype.show_num);

            final int rankTemp = openIndex;
            RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, openIndex, endIndex, true, (ret) -> {
                if(ret.failed()){
                    Log.temp.error("获取失败，rankSn={}, redisKey={}", rankSn, redisKey);
                    return;
                }
                int maxPage = (int) Math.ceil((double) total / RankParamKey.pageNum);
                Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
                rankData.setType(rankSn);
                rankData.setServer(Utils.getServerIdTo(serverId));
                rankData.setPage(page);
                rankData.setMaxPage(maxPage <= 0 ? 1 : maxPage);
                rankData.setTotalNum((int)total);
                rankData.setNextFreshTime(confRanktype.refresh_time);

                JsonArray json = ret.result();
                int sumSize = json.size();
                boolean isGuild = confRanktype.rank_type == RankParamKey.rankTypeGuild;
                int rank = rankTemp;
                Map<Long, Integer> idScoreMap = new HashMap<>();
                HumanData defaultHumanData = null;
                List<Long> humanIdList = new ArrayList<>();
                for (int i = 0; i < sumSize; i+=2) {
                    long id = Utils.longValue(json.getList().get(i));
                    int score = Utils.intValue(json.getList().get(i + 1));
                    idScoreMap.put(id, score);
                    if(!isGuild){
                        humanIdList.add(id);
                        continue;
                    }

                    rank++;
                    GuildData guildData = guildMap.get(id);
                    if(guildData == null){
                        Log.temp.error("未找到公会id={}", id);
                        continue;
                    }
                    Guild guild = guildData.getGuild();
                    if(guild == null){
                        Log.temp.error("未找到公会id={}", id);
                        continue;
                    }

                    HumanData humanData = rankHumanDataMap.get(guild.getLeaderId());
                    if(humanData != null && defaultHumanData == null){
                        defaultHumanData = humanData;
                    }
                    if(humanData == null){
                        humanData = defaultHumanData;
                    }
                    Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guild, score, rank, serverId, humanData);
                    rankData.addRankInfo(dInfo);
                    if(myGuildId == guild.getId()){
                        rankData.setMyRankInfo(dInfo);
                    }
                }
                if(!rankData.hasMyRankInfo()){
                    HumanData defaul = defaultHumanData;
                    RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(myGuildId), ret1-> {
                        if(ret1.failed()){
                            return;
                        }
                        int myRank = Utils.intValue(ret1.result());
                        RedisTools.getMyScore(EntityManager.redisClient, redisKey, myGuildId, ret2-> {
                            if (ret2.failed()) {
                                return;
                            }
                            int myScore = Utils.intValue(ret2.result());
                            GuildData guildData = guildMap.get(myGuildId);
                            if(guildData == null){
                                Log.temp.error("未找到公会id={}", myGuildId);
                                Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(null, myScore, myRank, serverId, defaul);
                                rankData.setMyRankInfo(dInfo);
                                msg.addRankDataList(rankData);
                                port.returnsAsync(pid, "rankList", msg.build());
                                return;
                            }
                            Guild guild = guildData.getGuild();
                            HumanData humanData = rankHumanDataMap.get(guild.getLeaderId());
                            if(humanData == null){
                                humanData = defaul;
                            }
                            Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guild, myScore, myRank, serverId, humanData);
                            rankData.setMyRankInfo(dInfo);
                            msg.addRankDataList(rankData);
                            port.returnsAsync(pid, "rankList", msg.build());
                        });
                    });
                    return;
                }
                if(confRanktype.rank_type == RankParamKey.rankTypeHuman){ // 玩家id
                    Map<Long, Integer> idScoreMap2 = idScoreMap;
                    HumanData.getList(humanIdList, new Class[]{Human.class, Human2.class}, (ret2) -> {
                        if (ret2.failed()) {
                            Log.temp.error("无法获取玩家数据");
                            return;
                        }
                        List<HumanData> hateDataList = ret2.result();
                        int rank2 = rankTemp;
                        for (HumanData humanData : hateDataList) {
                            if (humanData == null) continue;
                            rank2++;
                            RankInfo rankInfoTemp = new RankInfo(humanData.human,humanData.human2, idScoreMap2.get(humanData.human.getId()));
                            Define.p_rank_info.Builder rankInfo = rankInfoTemp.rank_info.toBuilder();
                            rankInfo.setRank(rank2);
                            rankData.addRankInfo(rankInfo.build());
                            if(myGuildId == humanData.human.getId()){
                                rankData.setMyRankInfo(rankInfo.build());
                            }
                        }
                        msg.addRankDataList(rankData);
                        port.returnsAsync(pid, "rankList", msg.build());
                    });
                    humanIdList = null;
                    idScoreMap = null;
                    return;
                }
                humanIdList = null;
                idScoreMap = null;
                msg.addRankDataList(rankData);
                port.returnsAsync(pid, "rankList", msg.build());
            });
        });
    }

    @DistrMethod
    public void hallGuild(long guildId, int score){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            return;
        }
        Guild guild = guildData.getGuild();
        if(guild == null){
            return;
        }
        MsgGvg.gvg_hall_s2c.Builder msg = MsgGvg.gvg_hall_s2c.newBuilder();
        msg.setSeasonTopGuild(GuildManager.inst().to_p_rank_info(guild, 1, score));
        List<Define.p_key_value_string> keyList = new ArrayList<>();
        String flagJSON = guild.getFlagJSON();
        if (!Utils.isEmptyJSONString(flagJSON)) {
            keyList = GuildManager.inst().to_p_guild_flag(flagJSON);
        }
        msg.setTopGuild(GuildManager.inst().to_p_gvg_guild(guild.getId(), guild.getName(), guild.getPower(), Utils.intValue(guild.getGameServerId()), keyList));
        port.returns("msg", msg.build());
    }

    @DistrMethod
    public void sendSettleGuildReward(Map<Long, LeagueVO> leagueVOMap){
        guildVoList.addAll(leagueVOMap.values());
        ttSec.reStart();
    }

    private void sendGuildReward(){
        if (guildVoList == null || guildVoList.isEmpty()) {
            ttSec.stop();
            return;
        }
        LeagueVO info = guildVoList.poll();
        if(info == null){
            return;
        }
        GuildData guildData = guildMap.get(info.voId);
        if(guildData == null){
            return;
        }
        // 一秒处理一个公会的奖励
        guildData.sendSettleGuildReward(info);
        if(guildVoList.isEmpty()){
            ttSec.stop();
        }
    }

    public void checkSyncGuild(GuildData guildData){
        if(guildData == null){
            return;
        }
        List<Long> memberIdList = guildData.getHumanIdList();
        if(memberIdList == null || memberIdList.isEmpty()){
            return;
        }
        checkSyncHumanIdList.addAll(memberIdList);
        if(!ttSecTwoCehck.isStarted()){
            ttSecTwoCehck.reStart();
        }
    }

    private void checkSyncHuman(){
        if(checkSyncHumanIdList == null || checkSyncHumanIdList.isEmpty()){
            ttSecTwoCehck.stop();
            Log.temp.info("同步完成");
            return;
        }
        int num = Math.min(checkSyncHumanIdList.size(), 200);
        List<Long> loadHumanIdList = new ArrayList<>();
        for(int i = 0; i < num; i++){
            long humanId = checkSyncHumanIdList.poll();
            if(!Utils.isExitRedisKey("entity.init.HumanBrief."+ humanId)){
                loadHumanIdList.add(humanId);
            }
        }
        if(checkSyncHumanIdList.isEmpty()){
            ttSecTwoCehck.stop();
        }
        HumanManager.inst().loadHumanIdSyncCross(loadHumanIdList, false, true);
    }

    @DistrMethod
    public void sendMsg_guild_schedule_s2c(List<Long> guildIdList, int state){
        // state 1开启 0关闭
        MsgGuild.guild_schedule_s2c.Builder msg = MsgGuild.guild_schedule_s2c.newBuilder();
        msg.addSchedules(HumanManager.inst().to_p_key_value(ParamKey.ActivitySchedule_Sn_4, state));
        List<Long> humanIdList = new ArrayList<>();
        for(long guildId : guildIdList){
            GuildData guildData = guildMap.get(guildId);
            if(guildData == null){
                continue;
            }
            humanIdList.addAll(guildData.getHumanIdList());
        }
        if(!humanIdList.isEmpty()){
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendMsgTo(humanIdList, msg.build());
        }
    }

    public void syncGuildLeagueCross(GuildData guildData, Param param) {
        if (Utils.isSameWeek2(Util.getOpenServerTime(C.GAME_SERVER_ID), Port.getTime())) {
            return;
        }
        if (guildData.getMemberNum() < GlobalConfVal.guildFamilySignupNum) {
            return;
        }
        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, guildData.getServerId(), res -> {
                try {
                    if (res.failed()) {
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                    proxy.updateGuildInfo(guildData.getId(), param);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("====跨服获取数据出问题", e);
        }
    }

    @DistrMethod
    public void getGuildHumanNum(long guildId){
        GuildData guildData = guildMap.get(guildId);
        if(guildData == null){
            port.returns("guildHumanNum", 0);
            return;
        }
        int num = guildData.getMemberNum();
        port.returns("guildHumanNum", num, "guildLv", guildData.getGuild().getLevel(), "guildName", guildData.getGuild().getName(), "guildFlagJSON", guildData.getGuild().getFlagJSON());
    }

    @DistrMethod
    public void sendMsg_guild_schedule_s2cWarm(List<Long> guildIdList, int state){
        // state 1开启 0关闭
        MsgGuild.guild_schedule_s2c.Builder msg = MsgGuild.guild_schedule_s2c.newBuilder();
        msg.addSchedules(HumanManager.inst().to_p_key_value(ParamKey.ActivitySchedule_Sn_4, state));
        List<Long> humanIdList = new ArrayList<>();
        for(long guildId : guildIdList){
            GuildData guildData = guildMap.get(guildId);
            if(guildData == null){
                continue;
            }
            humanIdList.addAll(guildData.getHumanIdList());
        }
        if(!humanIdList.isEmpty()){
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendMsgTo(humanIdList, msg.build());
        }
    }


    /**
     * 查看玩家信息
     * @param humanId
     * @param type
     */
    @DistrMethod
    public void getCrossHumanInfo(long humanId, int type){
        long pid = port.createReturnAsync();
        CrossHumanLoader.getHumanBrief(humanId, HumanBriefLoadType.BATTLE_INFO, res -> {
            if(res.failed()){
                port.returnsAsync(pid, "brief", null);
                return;
            }
            HumanBrief brief = res.result();
            if(brief == null){
                HumanData.getHumanDataAsync(humanId, HumanManager.inst().humanClasses, resHumanData -> {
                    if (resHumanData.failed()) {
                        Log.game.error("无法获取HumanData，roleId={}", humanId);
                        port.returnsAsync(pid, "brief", null);
                        return;
                    }
                    HumanData humanData = resHumanData.result();
                    if (humanData == null) {
                        Log.game.error("找不到角色信息HumanData，roleId = {}", humanId);
                        port.returnsAsync(pid, "brief", null);
                        return;
                    }
                    HumanBrief briefNew = HumanManager.inst().createHumanBrief(humanData, true);
                    getCrossHumanInfoResult(humanId, briefNew, pid);
                });
                return;
            }
            getCrossHumanInfoResult(humanId, brief, pid);
        });
    }

    private void getCrossHumanInfoResult(long humanId, HumanBrief brief, long pid){
        EntityManager.getEntityListAsync(Equip.class, humanId, (res1)-> {
            if (res1.failed()) {
                GuildData guildData = guildMap.get(brief.getGuildId());
                if (guildData == null) {
                    port.returnsAsync(pid, "brief", brief, "equip", null);
                    return;
                }
                Guild guild = guildData.getGuild();
                brief.setGuildName(guild.getName());
                brief.setGuildLv(guild.getLevel());
                brief.setGuildHumanNum(guildData.getMemberNum());
                port.returnsAsync(pid, "brief", brief, "guildName", guild.getName(), "guildLv", guild.getLevel(),
                        "guildHumanNum", guildData.getMemberNum(), "guildFlagJSON", guild.getFlagJSON(), "equip", null);
                return;
            }
            List<Equip> equipList = res1.result();
            Equip equipOne = null;
            for (Equip equip : equipList) {
                if (equip.getTab() == ParamKey.equipTab_1) {
                    equipOne = equip;
                    break;
                }
            }
            if (brief.getGuildId() == 0) {
                port.returnsAsync(pid, "brief", brief, "equip", equipOne);
                return;
            }
            GuildData guildData = guildMap.get(brief.getGuildId());
            if (guildData == null) {
                port.returnsAsync(pid, "brief", brief, "equip", equipOne);
                return;
            }
            Guild guild = guildData.getGuild();
            brief.setGuildName(guild.getName());
            brief.setGuildLv(guild.getLevel());
            brief.setGuildHumanNum(guildData.getMemberNum());
            port.returnsAsync(pid, "brief", brief, "guildName", guild.getName(), "guildLv", guild.getLevel(),
                    "guildHumanNum", guildData.getMemberNum(), "guildFlagJSON", guild.getFlagJSON(), "equip", equipOne);
        });
    }

    @DistrMethod
    public void sendSeasonResetMailSn(List<LeagueVO> leagueVOList) {
        sasonResetList.addAll(leagueVOList);
        if(!ttSeasonResetCehck.isStarted()){
            ttSeasonResetCehck.start(5 * Time.MIN);// 延迟5分钟执行
            if(Config.DATA_DEBUG){
                ttSeasonResetCehck.start(5 * Time.SEC);
            }
        }
    }
    private void checkSeasonResetMail(){
        if(sasonResetList == null || sasonResetList.isEmpty()){
            ttSeasonResetCehck.stop();
            return;
        }
        ttSeasonResetCehck.alterInterval(Port.getTime(), 5 * Time.SEC);
        LeagueVO vo = sasonResetList.poll();
        if(vo == null){
            return;
        }
        GuildData guildData = guildMap.get(vo.voId);
        if(guildData == null){
            return;
        }
        List<Long> humanIdList = guildData.getHumanIdList();
        if(humanIdList == null || humanIdList.isEmpty()){
            return;
        }
        int mailSn = vo.param.getInt("mailSn");
        int score = vo.param.getInt("score");
        int grade = vo.param.getInt("grade");
        int resetScore = vo.param.getInt("resetScore");
        int resetGrade = vo.param.getInt("resetGrade");

        JSONObject jo = new JSONObject();
        JSONObject joScore = new JSONObject();
        joScore.put(MailManager.MAIL_K_4, score);
        jo.put(MailManager.MAIL_PARAM_1, joScore);

        JSONObject joGrade = new JSONObject();
        joGrade.put(MailManager.MAIL_K_11, grade);
        jo.put(MailManager.MAIL_PARAM_2, joGrade);


        JSONObject joResetGrade = new JSONObject();
        joResetGrade.put(MailManager.MAIL_K_11, resetGrade);
        jo.put(MailManager.MAIL_PARAM_3, joResetGrade);

        JSONObject joResetScore = new JSONObject();
        joResetScore.put(MailManager.MAIL_K_4, resetScore);
        jo.put(MailManager.MAIL_PARAM_4, joResetScore);

        for(long humanId : humanIdList){
            MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", jo.toJSONString(), "", null);
        }
    }
}
