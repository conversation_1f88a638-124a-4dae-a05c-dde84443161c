package org.gof.demo.worldsrv.task;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.UnitManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.back.BackUtils;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.PocketLine;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.gm.GameDebugManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.human.RoleInfoKey;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.InformManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemData;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgReturn;
import org.gof.demo.worldsrv.msg.MsgTask;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.HumanScopeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.type.ITaskTypeData;
import org.gof.demo.worldsrv.task.type.TaskTypeDataFactory;
import org.gof.demo.worldsrv.task.type.TaskVO;
import org.gof.demo.worldsrv.task.type.dailydata.DailyTaskVO;
import org.gof.demo.worldsrv.task.type.maindata.MainTaskVO;

import java.util.*;

public class TaskManager extends ManagerBase {
    public static TaskManager inst() {
        return inst(TaskManager.class);
    }




    @Listener(EventKey.HUMAN_UPGRADE)
    public void _on_HUMAN_UPGRADE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_功能预告, TaskConditionTypeKey.TASK_TYPE_1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_1);
    }


    @Listener(EventKey.LOTTERY_SELECT)
    public void _on_LOTTERY_SELECT(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int type = Utils.getParamValue(param, "type", 0);
        int lotteryNum = Utils.getParamValue(param, "lotteryNum", 0);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_2, type, lotteryNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_3, type, lotteryNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_2, type, lotteryNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_2, type, lotteryNum);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2, lotteryNum, type, lotteryNum);
    }


    @Listener(EventKey.EQUIP_BOX_OPEN_NUM)
    public void _on_EQUIP_BOX_OPEN_NUM(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int num = Utils.getParamValue(param, "num", 0);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_4, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_4, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_4, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_4, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_4, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_4, num);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_4, num,num);
        // 累计开箱次数
        humanObj.getHuman3().setTotalBoxNum(humanObj.getHuman3().getTotalBoxNum() + num);
    }


    @Listener(EventKey.ITEM_CHANGE_ADD) // TODO 是否需要子类看需求
    public void _on_ITEM_CHANGE_ADD(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int itemSn = Utils.getParamValue(param, "itemSn", 0);
        int itemNum = Utils.getParamValue(param, "itemNum", 0);
//        Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
//                "taskType", TaskConditionTypeKey.TASK_TYPE_14, "paramType", TaskConditionTypeKey.PARAM_TYPE_2, "objs", new Object[]{itemSn, itemNum});

//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.待定, TaskConditionTypeKey.TASK_TYPE_10, itemSn, itemNum);
    }

    @Listener(EventKey.SKILL_UPGRADE)
    public void _on_SKILL_UPGRADE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
//        int num = Utils.getParamValue(param, "num", 0);
//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_13, num);
    }

    @Listener(EventKey.PET_UPGRADE)
    public void _on_PET_UPGRADE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
//        int num = Utils.getParamValue(param, "num", 0);
//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_14, num);
    }

    public void _on_(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }

//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.待定, TaskConditionTypeKey.TASK_TYPE_15, 1);
    }
    @Listener(EventKey.LOOK_AD)
    public void _on_LOOK_AD(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }

//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.待定, TaskConditionTypeKey.TASK_TYPE_17, 1);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH_FIRST_TODAY)
    public void _on_HUMAN_LOGIN_FINISH_FIRST_TODAY(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
//        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.待定, TaskConditionTypeKey.TASK_TYPE_18, 1);
    }



    @Listener(EventKey.FINISH_TASK)
    public void _on_FINISH_TASK(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int taskSn = Utils.getParamValue(param, "taskSn", 0);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_功能预告, TaskConditionTypeKey.TASK_TYPE_29, taskSn, 1);
    }


    @Listener(EventKey.TASK_CONDITION_TYPE)
    public void _on_TASK_CONDITION_TYPE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int type = Utils.getParamValue(param, "type", 0);
        int taskConditionType = Utils.getParamValue(param, "taskType", 0);
        int paramType =  Utils.getParamValue(param, "paramType", 0);
        if(paramType == 1){
            int addValue =  Utils.getParamValue(param, "addValue", 0);
            humanObj.operation.taskData.checkTaskPlan(humanObj, type, taskConditionType, addValue);
        } else if(paramType == 2){
            Object[] objs =  Utils.getParamValue(param, "objs", new Object[]{});
            humanObj.operation.taskData.checkTaskPlan(humanObj, type, taskConditionType, objs);
        } else {
            Log.temp.error("===未实现代码类型paramType={}, param={}", paramType, param);
        }
    }


    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _on_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        humanObj.operation.taskData.checkDailyTask(humanObj);
        if(Utils.isDebugMode()){
//            GameDebugManager.inst().onekey(humanObj, new String[]{"1", "1"});
            if(humanObj.getHuman().getLevel() < 10){
                humanObj.getHuman().setLevel(71);
//                humanObj.getHuman2().setRepSn(Utils.random(50,999));
                RankManager.inst().repRankUpdate(humanObj);
                HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, RoleInfoKey.ROLE_ATTR_LVL, RoleInfoKey.ROLE_ATTR_EXP);
//                ProduceManager.inst().produceAdd(humanObj,2,5000,MoneyItemLogKey.测试接口);
//                String ALPHABET = "abcdefghijklmnopqrstuvwxyz";
//                int length = Utils.random(5) + 1; // 随机长度1到5
//                StringBuilder builder = new StringBuilder(length);
//                for (int i = 0; i < length; i++) {
//                    int index = Utils.random(ALPHABET.length());
//                    builder.append(ALPHABET.charAt(index));
//                }
//                String name = builder.toString();
//                GuildManager.inst()._msg_guild_create_c2s(humanObj, name);
                //如果升级 给前端发布消息
                Event.fire(EventKey.HUMAN_UPGRADE, "humanObj", humanObj);
            }
        }
        checkMainTaskPlan(humanObj);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_106);
    }

    private void checkMainTaskPlan(HumanObject humanObj){
        int sn = 1;
        TaskData taskData = humanObj.operation.taskData;
        for(MainTaskVO vo : taskData.mainTaskMap.values()){
            if(sn < vo.taskSn){
                sn = vo.taskSn;
            }
            ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
            if(idata == null){
                Log.temp.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, vo.getConditionType());
                continue;
            }
            idata.checkPlan(humanObj, vo, 0);// 注意检测只能是0,正常增加进度後检测是实际值
            if(vo.getStatus() == TaskConditionTypeKey.TASK_STATUS_已领取奖励){
                TaskVO voNew = taskData.acceptMainTask(humanObj, vo.getNextSn());
                taskData.removeMainTask(vo.getTaskSn());
                taskData.saveMainTask();
                if(voNew != null){
                    TaskManager.inst().sendTaskInfo(humanObj, voNew);
                    TaskManager.inst().sendMsg_task_commit_s2c(humanObj, TaskConditionTypeKey.TASK_主线, voNew.taskSn);
                }
                taskData.addFinishTaskSn(humanObj.id, vo.getTaskSn());
            }
        }
        int initSn = 1;
        int loopNum = 1000;
        ConfMainTask conf = ConfMainTask.get(initSn);
        List<Integer> finishMainTaskSnList = humanObj.operation.taskData.finishMainTaskSnList;
        while (conf != null && conf.sn != sn){
            loopNum--;
            if(loopNum <= 0){
                break;
            }
            initSn++;

            if(finishMainTaskSnList.contains(conf.sn)){
                conf = ConfMainTask.get(initSn);
                continue;
            }
            finishMainTaskSnList.add(conf.sn);
            if(conf.next == sn){
                break;
            }
            conf = ConfMainTask.get(initSn);
        }
        humanObj.operation.taskData.initFinishTask(humanObj.id);
        humanObj.operation.taskData.saveTask();
        Event.fire(EventKey.FINISH_TASK, "humanObj", humanObj, "taskSn", 0);

    }

    private void test(HumanObject humanObj){
//        humanObj.getHuman().setRepSn(50);
//        humanObj.getHuman().setRepSnClient(50);
        humanObj.getHumanExtInfo().setEquipBoxLv(10);

//        GameDebugManager.inst().pay(humanObj, new String[]{"1"});

        Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
                "taskType", TaskConditionTypeKey.TASK_TYPE_20, "paramType", TaskConditionTypeKey.PARAM_TYPE_1);


        GameDebugManager.inst().add(humanObj, new String[]{"3", "100000"});
        GameDebugManager.inst().add(humanObj, new String[]{"1001", "100"});
        GameDebugManager.inst().add(humanObj, new String[]{"1012", "100"});
        GameDebugManager.inst().add(humanObj, new String[]{"1013", "100"});
        for(int sn : TokenItemType.getTokenItemValues()){
            MoneyManager.inst().produceMoneyAdd(humanObj, sn, 1000, MoneyItemLogKey.测试接口);
        }
        PropCalc propCalc = new PropCalc();
        propCalc.plus("1001", 10000000);
        propCalc.plus("1002", 100000000);
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.skill, propCalc.toJSONStr());
        UnitManager.inst().propCalc(humanObj);
//        MallManager.inst()._msg_draw_card_c2s(humanObj, 1, 15, false);
//        for(MainTaskVO vo : humanObj.operation.taskData.mainTaskMap.values()){
//            vo.setPlan(vo.getSumPlan());
//            vo.checkStatus();
//        };

        if(Utils.isDebugMode()){
            return;
        }

        Map<Integer , Map<Integer, Integer>> itemNumMap = new HashMap<>();
        for(ConfGoods confGoods : ConfGoods.findAll()){
            Map<Integer, Integer> map = itemNumMap.get(confGoods.classify);
            if(map == null){
                map = new HashMap<>();
                itemNumMap.put(confGoods.classify,map);
            }
            map.put(confGoods.sn, 9);
//            if(confGoods.type == 26){
//                GameDebugManager.inst().add(humanObj, new String[]{String.valueOf(confGoods.sn), "200"});
//            } else {
//                if(confGoods.sn >= 205 && confGoods.classify == 1){
//                    Log.temp.info("===sn={}", confGoods.sn);
//                }
//                GameDebugManager.inst().add(humanObj, new String[]{String.valueOf(confGoods.sn), "99999"});
//            }
//            try {
//                Thread.sleep(100L);
//            } catch (Exception e){
//                Log.temp.error("e={}", e);
//            }
        }
        long time1 = System.nanoTime();
        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(1), MoneyItemLogKey.测试接口);
        long time2 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   1    use = {}", (time2 - time1) / 1000000);
        }

        ItemData itemData = humanObj.operation.itemData;

        long time3 = System.nanoTime();
        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(2), MoneyItemLogKey.测试接口);
        long time9 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   2    use ms = {}", (time9 - time3) / 1000000 );
        }

        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(3), MoneyItemLogKey.测试接口);
        long time10 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   3    use ms = {}", (time10 - time9) / 1000000);
        }
        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(4), MoneyItemLogKey.测试接口);
        long time4 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   4    use ms = {}", (time4 - time10) / 1000000);
        }

        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(5), MoneyItemLogKey.测试接口);
        long time5 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   5    use ms = {}", (time5 - time4) / 1000000);
        }
        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(6), MoneyItemLogKey.测试接口);
        long time6 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   6    use ms = {}", (time6 - time5) / 1000000);
        }
        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(7), MoneyItemLogKey.测试接口);
        long time7 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   7    use ms = {}", (time7 - time6) / 1000000);
        }

        ProduceManager.inst().produceAll(humanObj, itemNumMap.get(8), MoneyItemLogKey.测试接口);
        long time8 = System.nanoTime();
        if(S.isTestLog) {
            Log.temp.info("====   8    use ms = {}", (time8 - time7) / 1000000);
        }

        GameDebugManager.inst().add(humanObj, new String[]{"3", "10000"});
        GameDebugManager.inst().add(humanObj, new String[]{"1001", "100"});
        int zone = Util.getServerIdZoneCode(humanObj.getHuman().getServerId());
        InformManager.inst().sendMsgChatMessage(HumanScopeKey.ALL, 0, Inform.世界, humanObj.id, zone, 1, "这是一个测试", null, null);
        int num = 100;
        while (num > 0){
            num --;
            for(MainTaskVO vo : humanObj.operation.taskData.mainTaskMap.values()){
                for(int i = 0; i < vo.getSumPlan(); i++){
                    if(vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_4){
                        EquipManager.inst().equip_box_open_c2s(humanObj);
                    } else
                    if(vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_3){
                        // 抽卡
                    } else if(vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_16
                            || vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_8
                            || vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_13
                            || vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_20
                            || vo.getConditionType() == TaskConditionTypeKey.TASK_TYPE_1){
                        vo.setPlan(vo.getSumPlan());
                    } else {
                        vo.setPlan(vo.getSumPlan());
                    }
                }

                vo.checkStatus();
                if(vo.isFinish()){
                    TaskManager.inst()._msg_task_commit_c2s(humanObj, vo.getType(), vo.getTaskSn());
                }
            }
        }

    }


    public void _msg_task_commit_all_c2s(HumanObject humanObj, int type) {
        switch (type){
            case TaskConditionTypeKey.TASK_主线:
                taskCommitAllMain(humanObj);
                break;
            case TaskConditionTypeKey.TASK_每日:
                taskCommitAllDaily(humanObj);
                break;
            case TaskConditionTypeKey.TASK_冒险任务:
            case TaskConditionTypeKey.TASK_成就任务:
                break;
            default:
                Log.temp.error("===未实现该类型代码,{}", type);
                break;
        }


    }
    private void taskCommitAllMain(HumanObject humanObj){
        Map<Integer, MainTaskVO> mainTaskMap = humanObj.operation.taskData.mainTaskMap;
        for(MainTaskVO vo : mainTaskMap.values()){
            if(vo.isFinish()){
                _msg_task_commit_c2s(humanObj, vo.getType(), vo.getTaskSn());
            }
        }
    }

    private void taskCommitAllDaily(HumanObject humanObj){
        Map<Integer, DailyTaskVO> dailyTaskMap = humanObj.operation.taskData.dailyTaskMap;
        for(DailyTaskVO vo : dailyTaskMap.values()){
            if(vo.isFinish()){
                _msg_task_commit_c2s(humanObj, vo.getType(), vo.getTaskSn());
            }
        }
    }


    public void _msg_task_req_daily_box_c2s(HumanObject humanObj) {
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyPoint.getType());
        if(info == null){
            return;
        }
        int value = info.getValue();
        List<Integer> snList = Utils.strToIntList(info.getParam());
        Map<Integer, Integer> snNumMap = new HashMap<>();
        boolean isUp = false;
        for(ConfDailyTaskReward conf : ConfDailyTaskReward.findAll()){
            if(snList.contains(conf.sn)){
                continue;
            }
            if(conf.point > value){
                continue;
            }
            isUp = true;
            snList.add(conf.sn);
            snNumMap = Utils.intArrToIntMap(snNumMap, conf.reward);
        }
        if(isUp){
            info.setParam(Utils.listToString(snList));
            humanObj.saveDailyResetRecord();
            ProduceManager.inst().produceAdd(humanObj, snNumMap, MoneyItemLogKey.每日任务活跃度奖励);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, snNumMap);
        }
        sendMsg_task_req_daily_box_s2c(humanObj, snList);
    }

    private void sendMsg_task_req_daily_box_s2c(HumanObject humanObj, List<Integer> snList){
        MsgTask.task_req_daily_box_s2c.Builder msg = MsgTask.task_req_daily_box_s2c.newBuilder();
        msg.addAllBoxList(snList);
        humanObj.sendMsg(msg);
    }


    public void _msg_task_action_finish_channel_c2s(HumanObject humanObj, String channel) {

    }

    public void _msg_task_commit_c2s(HumanObject humanObj, int type, long taskId){
        TaskData taskData = humanObj.operation.taskData;
        switch (type){
            case TaskConditionTypeKey.TASK_主线:
                taskData.submitMainTask(humanObj, (int)taskId);
                break;
            case TaskConditionTypeKey.TASK_每日:
                taskData.submitDailyTask(humanObj, (int)taskId);
                break;
            case TaskConditionTypeKey.TASK_冒险任务:
                taskData.submitAdventureTask(humanObj, (int)taskId, true);
                break;
            case TaskConditionTypeKey.TASK_成就任务:
                taskData.submitAchievementTask(humanObj, (int)taskId);
                break;
            case TaskConditionTypeKey.TASK_飞宠成就:
                taskData.submitFlyAchieveTask(humanObj, (int)taskId);
                break;
            case TaskConditionTypeKey.TASK_武道会任务:
                taskData.submitKungFuRaceTask(humanObj, (int)taskId);
                break;
        }
    }

    public void sendMsg_task_commit_s2c(HumanObject humanObj, int type, int taskId){
        MsgTask.task_commit_s2c.Builder msg = MsgTask.task_commit_s2c.newBuilder();
        msg.setType(type);
        msg.setTaskId(taskId);
        humanObj.sendMsg(msg);
    }


    public void sendTaskInfo(HumanObject humanObj, TaskVO vo){
        if(vo.getType() == TaskConditionTypeKey.TASK_战令 || vo.getType() == TaskConditionTypeKey.TASK_活动){
            return;
        }
        if(vo.getType() == TaskConditionTypeKey.TASK_回归任务){
            MsgReturn.return_task_update_s2c.Builder msg = MsgReturn.return_task_update_s2c.newBuilder();
            msg.setActType(vo.getType());
            msg.addTaskList(BackUtils.to_p_act_task(vo));
            humanObj.sendMsg(msg);
//            Log.temp.info("===return_task_update_s2c={}", msg);
            return;
        }
        MsgTask.task_update_s2c.Builder msg = MsgTask.task_update_s2c.newBuilder();
        msg.addTaskList(to_p_task(vo));
        humanObj.sendMsg(msg);
//        Log.temp.info("===task_update_s2c={}", msg);
    }

    public void sendMsg_task_all_s2c(HumanObject humanObj){
        MsgTask.task_all_s2c.Builder msg = MsgTask.task_all_s2c.newBuilder();
        List<TaskVO> taskVoList = new ArrayList<>();
        taskVoList.addAll(humanObj.operation.taskData.mainTaskMap.values());
        taskVoList.addAll(humanObj.operation.taskData.dailyTaskMap.values());
        taskVoList.addAll(humanObj.operation.taskData.adventureTaskMap.values());
        taskVoList.addAll(humanObj.operation.taskData.achievementTaskMap.values());
        taskVoList.addAll(humanObj.operation.taskData.flyAchieveMap.values());
        taskVoList.addAll(humanObj.operation.taskData.kungFuRaceTaskMap.values());
        if(humanObj.operation.fishData != null && humanObj.operation.fishData.getHomeFish() != null){
            taskVoList.addAll(humanObj.operation.fishData.getDailyTasks().values());
            taskVoList.addAll(humanObj.operation.fishData.getFishGroundTasks().values());
        }
        for(TaskVO vo : taskVoList){
            msg.addTaskList(to_p_task(vo));
        }
        humanObj.sendMsg(msg);
        if(S.isTestLog){
            Log.temp.info("===msg={}", msg);
        }
    }

    public void sendMsg_task_achievement_s2c(HumanObject humanObj){
        // 通知成就统计信息
        MsgTask.task_achievement_s2c.Builder achievementMsg = humanObj.operation.taskData.to_task_achievement_s2c();
        humanObj.sendMsg(achievementMsg);
    }

    public Define.p_task.Builder to_p_task(TaskVO vo){
        Define.p_task.Builder dInfo = Define.p_task.newBuilder();
        dInfo.setTaskId(vo.taskSn);
        dInfo.setState(vo.getStatus());
        dInfo.setCount(vo.getPlan());
        dInfo.setType(vo.getType());
        return dInfo;
    }
    public void sendMsg_task_daily_point_s2c(HumanObject humanObj){
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyPoint.getType());
        if (info == null) {
            Log.human.error("===HumanDailyResetInfo为null,type={}", DailyResetTypeKey.dailyPoint);
            return;
        }

        List<Integer> snList = Utils.strToIntList(info.getParam());
        MsgTask.task_daily_point_s2c.Builder msg = MsgTask.task_daily_point_s2c.newBuilder();
        msg.setDailyPoint(info.getValue());

        for(ConfDailyTaskReward conf : ConfDailyTaskReward.findAll()){
            int state = TaskConditionTypeKey.TASK_BOX_NORMAL;
            if(snList.contains(conf.sn)){
                state = TaskConditionTypeKey.TASK_BOX_RECEIVED;
            }
            msg.addBoxList(HumanManager.inst().to_p_key_value(conf.sn, state));
        }
        humanObj.sendMsg(msg);
    }

    @Listener(EventKey.INSTANCE_PASS)
    public void _on_INSTANCE_PASS(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
//        int repType = Utils.getParamValue(param, "repType", 0);
//        int num = Utils.getParamValue(param, "num", 1);
//        int repSn = Utils.getParamValue(param, "repSn", 0);
    }

    public void checkRepType(HumanObject humanObj, int repType,int num) {
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_11, repType, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_11, repType, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_21, repType);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_11, repType, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_11, repType, num);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_11, num,repType,num);
    }

    /**
     * 成就进度代办
     * @param param
     */
    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.ACHIEVEMENT_PROGRESS)
    public void pocketLine_ACHIEVEMENT_PROGRESS(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        int taskType = jo.getIntValue("taskType");
        Object[] objs = jo.getObject("objs", Object[].class);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, taskType, objs);
        Log.task.info("pocketLine_ACHIEVEMENT_PROGRESS humanId:{} taskType={} objs={}", humanObj.id, taskType, objs);
    }

    /**
     * 获取成就统计信息
     * @param humanObj
     */
    public void _msg_task_achievement_c2s(HumanObject humanObj) {
        sendMsg_task_achievement_s2c(humanObj);
    }

    /**
     * 领取成就统计奖励
     * @param humanObj
     */
    public void _msg_task_achievement_reward_c2s(HumanObject humanObj) {
        int[] reward = humanObj.operation.taskData.receiveAchievementTotalRewards(humanObj);
        if(reward == null){
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, reward, MoneyItemLogKey.成就统计奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, reward);
    }

    // region 飞宠成就
    public void _msg_task_fly_achievement_c2s(HumanObject humanObj) {
        List<Integer> recvList = humanObj.operation.taskData.getFlyTotalAchieveRecvIdList();
        int sn = recvList.stream().max(Integer::compareTo).orElse(0);
        MsgTask.task_fly_achievement_s2c.Builder msg = MsgTask.task_fly_achievement_s2c.newBuilder();
        msg.setGetId(sn);
        msg.setNowId(ConfFlyTotalAchievement.get(sn + 1) == null ? sn : sn + 1);
        msg.setProgress(humanObj.operation.taskData.getFlyAchieveFinishNum());
        humanObj.sendMsg(msg);
    }

    public void _msg_task_fly_achievement_reward_c2s(HumanObject humanObj) {
        List<Integer> recvList = humanObj.operation.taskData.getFlyTotalAchieveRecvIdList();
        int sn = recvList.stream().max(Integer::compareTo).orElse(0);
        ConfFlyTotalAchievement conf = ConfFlyTotalAchievement.get(sn + 1);
        if (conf == null) {
            return;
        }
        int finishNum = humanObj.operation.taskData.getFlyAchieveFinishNum();
        if (finishNum < conf.num) {
            Inform.sendMsg_error(humanObj, ErrorTip.RewardNotMeetCondition);
            return;
        }
        // 先记录领取记录再发奖励
        humanObj.operation.taskData.addFlyTotalAchieveRecvId(conf.sn);
        // 发奖励
        List<Define.p_reward> rewardList = ProduceManager.inst().rewardProduceToMsg(humanObj, conf.reward, MoneyItemLogKey.飞宠成就);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardList);
        _msg_task_fly_achievement_c2s(humanObj);
    }
    // endregion 飞宠成就
}
