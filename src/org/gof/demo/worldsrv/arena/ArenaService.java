package org.gof.demo.worldsrv.arena;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfPvpReward;
import org.gof.demo.worldsrv.config.ConfRanktype;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.entity.Mail;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgArena;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@DistrClass(
		servId = D.SERV_ARENA,
        importClass = {List.class, Map.class},
		localOnly = false
)
/**
 * 竞技场（本服和跨服）
 * <AUTHOR>
 * @Date 2024/6/27
 * @Param
 */
public class ArenaService extends GameServiceBase {

	private TickTimer scheduleTimer = new TickTimer(10000);//调度的处理
	private TickTimer tickTimer = new TickTimer(30 * Time.SEC);
	private Map<Integer, Integer> zoneMaxGroupMap = new HashMap<>();

	// 本服时，服务器id
	private List<Integer> serverIdListNow = new ArrayList<>();

	private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理

	// 賽季奖励
	private List<String> serverSeasonList = new ArrayList<>();
	// 每日奖励
	private List<Integer> serverDailyList = new ArrayList<>();

	private Map<Integer, Map<Long, Define.p_rank_info>> robotMap = new ConcurrentHashMap<>();
	private Map<Integer, Map<Long, String>> robotJoMap = new ConcurrentHashMap<>();

	private Map<Integer, Map<Long, String>> nextRobotJoMap = new ConcurrentHashMap<>();

	private Map<Long, Define.p_arena_rank> robotArenaMap = new HashMap<>();


	private boolean isSend = false;
	private int num = 0;

	@Override
	protected void init() {

		tickTimer.reStart();
		checkServerId();

		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN);
		long timeNext = Utils.formatTimeToLong(confGlobal.strValue);
		long timeNow = Port.getTime();
		Log.temp.info("===初始化本服竞技场服务, {}", timeNext);
		ConfGlobal conf = ConfGlobal.get("pvp_cross_sever");
		long time = Utils.formatTimeToLong(conf.strValue);
		if(time < timeNow){
			int serNoId = Utils.getServerIdTo(Config.SERVER_ID);
			if(conf.intArray[0] <= serNoId && serNoId <= conf.intArray[1]){
				// 跨服
				Log.temp.info("本服竞技场赛季已经结束，不初始化， confSeason={}, timeNow={}, serverId {} -> {} timeNext={}, serverNoId={}",
						 confGlobal.value, timeNow,conf.intArray[0], conf.intArray[1], time, serNoId);
				return;
			}
		}

		Utils.getRedisStrValue(RedisKeys.arenaSeason + Config.SERVER_ID, res -> {
			if (res.failed()) {
				Log.game.error("get arenaSeason from redis failed! serverId={}", Config.SERVER_ID, res.cause());
			} else {
				int season = Utils.intValue(res.result());
				if (season > ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN).value) {
					return;
				}
			}
			initLoadRobot(Config.SERVER_ID);
		});

	}

    private void initLoadRobot(int serverId) {
        if (!S.isGameServer) {
			return;
		}

//		int serverId = C.GAME_SERVER_ID;
//		String redisKey = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);

		// 机器人最大数
//		List<ConfRobot> confList = ConfRobot.findBy(ConfRobot.K.type, 1);
		// 策划要求每个阶段上下25，每分一个

		RedisTools.get(EntityManager.redisClient, RedisKeys.arenaRobotMaxNum + serverId,  ret1 -> {
            if (!ret1.succeeded()) {
				long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
				String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
				String keyRedis = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);
				ArenaManager.inst().initRobot(keyRedis, GlobalConfVal.getZone(serverId), serverId, dateStr, false);
				return;
			}
			int maxNum = Utils.intValue(ret1.result());
			String redisKey = RedisKeys.arenaRobot + serverId;
            if (maxNum <= 510) {
				maxNum = 510;
			}
            for (int i = 1; i <= maxNum; i++) {
				RedisTools.get(EntityManager.redisClient, redisKey + i,  ret -> {
                    if (ret.failed()) {
						Log.temp.error("===加载机器人失败， {},{}", redisKey, ret.cause());
						return;
					}
					JSONObject jo = Utils.toJSONObject(ret.result());
                    if (jo == null || jo.isEmpty()) {
						return;
					}
					long robotId = jo.getLongValue(ParamKey.idKey);
					Map<Long, String> joMap = robotJoMap.get(serverId);
                    if (joMap == null) {
						joMap = new HashMap<>();
						robotJoMap.put(serverId, joMap);
					}
					joMap.put(robotId, ret.result());


                    Map<Long, Define.p_rank_info> map = robotMap.get(serverId);
                    if (map == null) {
						map = new HashMap<>();
						robotMap.put(serverId, map);
					}
					map.put(robotId, ArenaManager.inst().robot_p_rank_info(jo, 0, 0));
				});
			}
		});

//		for (ConfRobot confRobot : confList) {
//			int min = confRobot.point - 25;
//			int max = confRobot.point + 25;
//			for (; min <= max; min++) {
//				++id;
//				RedisTools.get(EntityManager.redisClient, redisKey + id,  ret -> {
//					JSONObject jo = Utils.toJSONObject(ret.result());
//					long robotId = jo.getLongValue(ParamKey.idKey);
//					robotMap.put(robotId, ArenaManager.inst().robot_p_rank_info(jo, 0, 0));
//					robotJoMap.put(robotId, ret.result());
//				});
//			}
//		}
	}


	/**
	 * 构造函数
	 *
	 * @param port
	 */
	public ArenaService(GamePort port) {
		super(port);
	}

	@Override
	public void pulseOverride() {
		long timeNow = Port.getTime();
        if (tickTimer.isPeriod(timeNow)) {
			tickTimer.stop();
		}
        if (num < 4) {
			++num;
//			test(RedisKeys.admin_server_date_zone_group, 30973);
		}

//		if(checkServerIdTT.isPeriod(timeNow)){
//			checkServerId();
//		}
	}

    private void checkServerId() {
		int serverTag = C.GAME_SERVER_ID;
        if (S.isBridge) {
			serverTag = 0;
		}
		List<Integer> serverListTemp = Util.getServerTagList(serverTag);
        if (serverListTemp.isEmpty()) {
            if (Config.DATA_DEBUG) {
				if (C.GAME_SERVER_ID == 30059) {
					serverListTemp.add(40059);
				} else {
					serverListTemp.add(C.GAME_SERVER_ID);
				}
			}
		}
        if (serverListTemp.isEmpty()) {
			Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
			return;
		}
		List<Integer> serverList = new ArrayList<>();
        if (S.isBridge) {
            for (int serverId : serverListTemp) {
				int groupIndex = Utils.getBridgeRankGroup(serverId);
                int serverNo = Utils.getBridgeServerNo(GlobalConfVal.getZone(serverId), groupIndex);
                if (serverNo == C.BRIDGE_SERVER_ID) {
					serverList.add(serverId);
				}
			}
		} else {
			serverList.addAll(serverListTemp);
		}
        if (serverList.isEmpty()) {
			Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
			return;
		}

        for (Integer serverId : serverListTemp) {
            if (!serverIdListNow.contains(serverId)) {
				serverIdListNow.add(serverId);
				loadServerIdArena(serverId);
			}
		}


	}

	@DistrMethod
    public void loadServer(List<Integer> serverIdList) {
        if (S.isBridge) {
			return;
		}
		Collection<Integer> diffAdd = CollectionUtils.subtract(serverIdList, serverIdListNow);
        if (diffAdd.isEmpty()) {
			return;
		}
		Log.temp.error("===本服id={},  serverIds={}, serverIdListNow={}", C.GAME_SERVER_ID, diffAdd, serverIdListNow);
        for (int serverId : diffAdd) {
			serverIdListNow.add(serverId);
			loadServerIdArena(serverId);
		}
	}

    private void loadServerIdArena(int serverId) {
		if (S.isBridge) {
			return;
		}
		ArenaManager.inst().getArenaRankKey(serverId, S.isBridge, res -> {
			String key = res.result();
			int zone = GlobalConfVal.getZone(serverId);
			// 判断列表是否存在
			RedisTools.exists(EntityManager.getRedisClient(), key, h -> {
				if (h.failed()) {
					return;
				}
				boolean exists = h.result();
				if (!exists) {
					long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
					String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
					ArenaManager.inst().initRobot(key, zone, serverId, dateStr);
				} else {
					// 判断列表是否存在
					String robotKey = ArenaManager.inst().getRobotRedisKey(serverId, S.isBridge);
					long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
					String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
					RedisTools.exists(EntityManager.getRedisClient(), robotKey + 1, h2 -> {
						if (h2.failed()) {
							return;
						}
						boolean existsRobot = h2.result();
						if (!existsRobot) {
							ArenaManager.inst().initRobot(key, zone, serverId, dateStr, false);
						}
					});
				}
			});
		});
	}

	@DistrMethod
    public void getArenaRival(long humanId, int serverId, int winNum) {
		if(Config.DATA_DEBUG){
			Log.temp.info("===查bug, humanId={}, serverId={}", humanId, serverId);
		}
		long pid = port.createReturnAsync();
		List<Define.p_arena_role> dInfoList = new ArrayList<>();
		Log.temp.info("===getArenaRival, humanId={}, serverId={}, winNum={}", humanId, serverId, winNum);
		if (robotJoMap.isEmpty()) {
			if(Config.DATA_DEBUG){
				Log.temp.error("===查bug, humanId={}, account={}, robotJoMap={}", humanId, serverId, robotJoMap);
			}
//			initLoadRobot(serverId);
			long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
			String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
			String keyRedis = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);
			ArenaManager.inst().initRobot(keyRedis, GlobalConfVal.getZone(serverId), serverId, dateStr, false);
            port.returnsAsync(pid, "dInfoList", dInfoList);
			return;
		}

		Map<Long, Integer> idRankMap = new HashMap<>();
		String key = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);

		int rank = Utils.getRedisRank(key, String.valueOf(humanId));

		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.pvp_match_range.SN);
		int rangeNum = confGlobal.value;// 保底最大值
		int size = confGlobal.strArray.length;
		int index = 3;// 0的时候第4条开始
		index += winNum;
        if (index < 0) {
			index = 0;
		}
        if (index > size - ParamKey.rivalNum) {
			index = size - ParamKey.rivalNum;
		}
		List<Integer> rankList = new ArrayList<>();
		for (int i = 0; i < ParamKey.rivalNum; i++) {
			double[] arrDouble = Utils.strToDoubleArray(confGlobal.strArray[index]);
			index++;
			int randomRan = Utils.random((int) Math.ceil(rank * arrDouble[1]), (int) Math.ceil(rank * arrDouble[0]) + rangeNum);
			int loopNum = 10;
			while (rankList.contains(randomRan) || randomRan == rank) {
				loopNum--;
				randomRan = Utils.random((int) Math.ceil(rank * arrDouble[1]), (int) Math.ceil(rank * arrDouble[0]) + rangeNum);
				if (loopNum <= 0) {
					break;
				}
			}
			if (rankList.contains(randomRan) || randomRan == rank) {
				continue;
			}
			rankList.add(randomRan);
		}

		List<Long> idList = new ArrayList<>();
		idList.add(humanId);

		int min = rank - 10  <= 0 ? 0 : rank - 10;
		int max = rank + rangeNum;
		Map<Long, Integer> idScoreMap = new HashMap<>();
		RedisTools.getRankListByIndex(EntityManager.redisClient, key, min, max, true, ret -> {
			if (!ret.succeeded()) {
				Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
				port.returnsAsync(pid,"dInfoList", dInfoList);
				return;
			}
			JsonArray json = ret.result();
			int sumSize = json.size();
			Set<Integer> rankSet = new HashSet<>();
			Set<Long> idSet = new HashSet<>(idList);

			int loopNum = 50;
			while (idSet.size() < 6 && loopNum-- > 0) {
				int rankTemp = Utils.random(min, max);
				if (rankSet.add(rankTemp)) {
					int indexTemp = (rankTemp - min) * 2;
					if (indexTemp < sumSize) {
						long id = Utils.longValue(json.getList().get(indexTemp));
						if (idSet.add(id)) {
							idRankMap.put(id, rankTemp);
							idScoreMap.put(id, Utils.intValue(json.getList().get(indexTemp + 1)));
						}
					}
				}
			}
			if (loopNum <= 0) {
				Log.temp.info("未能在规定次数内获取到足够的对手id，当前获得的id数量: {}", idSet.size());
			}

			List<Long> humanIdList = new ArrayList<>();
            for (long id : idRankMap.keySet()) {
                if (!ArenaManager.inst().isRobot(id)) {
					humanIdList.add(id);
				}
			}

            if (humanIdList.isEmpty()) {
                for (Map.Entry<Long, Integer> entry : idRankMap.entrySet()) {
					long id = entry.getKey();
					int rankTemp = entry.getValue();
                    if (id == humanId) {
						continue;
					}
                    if (ArenaManager.inst().isRobot(id)) {
						String jsonTemp = robotJoMap.getOrDefault(serverId, new HashMap<>()).get(id);
                        if (jsonTemp != null && !jsonTemp.isEmpty()) {
							JSONObject jo = Utils.toJSONObject(jsonTemp);
                            if (jo.isEmpty()) {
								continue;
							}
							dInfoList.add(ArenaManager.inst().robot_to_p_arena_role(jo, rankTemp, idScoreMap.get(id), Config.SERVER_ID, ParamKey.sourceKey_0));
						}
					}
				}
                port.returnsAsync(pid, "dInfoList", dInfoList);
				return;
			}

            HumanData.getList(humanIdList, HumanManager.inst().humanClasses, (res) -> {
                if (res.failed()) {
					Log.human.error("MemberCallback getMemberAsync error", res.cause());
                    port.returnsAsync(pid, "dInfoList", dInfoList);
					return;
				}
				List<HumanData> humanDataList = res.result();
				Map<Long, HumanData> idDataMap = new HashMap<>();
                for (HumanData humanData : humanDataList) {
					idDataMap.put(humanData.human.getId(), humanData);
				}
				humanDataList.clear();
                for (Map.Entry<Long, Integer> entry : idRankMap.entrySet()) {
					long id = entry.getKey();
					int rankTemp = entry.getValue();
					if (id == humanId) {
						continue;
					}
					if (ArenaManager.inst().isRobot(id)) {
						String jsonTemp = robotJoMap.getOrDefault(serverId, new HashMap<>()).get(id);
						if (jsonTemp != null && !jsonTemp.isEmpty()) {
							JSONObject jo = Utils.toJSONObject(jsonTemp);
							if (jo.isEmpty()) {
								continue;
							}
							dInfoList.add(ArenaManager.inst().robot_to_p_arena_role(jo, rankTemp, idScoreMap.get(id), Config.SERVER_ID, ParamKey.sourceKey_0));
						}
                    } else {
						HumanData humanData = idDataMap.get(id);
                        if (humanData != null) {
							dInfoList.add(ArenaManager.inst().to_p_arena_role(humanData, rankTemp, idScoreMap.get(id)));
						}
					}
				}
                port.returnsAsync(pid, "dInfoList", dInfoList);
			});
		});

	}

    private void saveArena(int serverId) {
//		List<String> keyList = new ArrayList<>();
//		keyList.add(RedisKeys.admin_pvp_group);
//		for(int serverId : serverIdList){
//			keyList.add(String.valueOf(groupIndex));
//			keyList.add(String.valueOf(serverId));
//		}
//		EntityManager.redisClient.zadd(keyList, f->{
//			if(!f.succeeded()){
//				Log.temp.error("===保存失败，keyList={}", keyList);
//			}
//		});
//		RedisTools.set(EntityManager.getRedisClient(), RedisKeys.admin_pvp_group_max, String.valueOf(groupIndex));
	}

	@ScheduleMethod(DataResetService.CRON_DAY_ZERO)
	public void _CRON_DAY_ZERO() {
		isSend = false;
		serverDailyList.clear();
		serverSeasonList.clear();
		robotMap.clear();


		if(S.isGameServer){
			int week = Utils.getDayOfWeek();// 周日1,周一2...周六7
			if(week == 2){// 是周1
				robotJoMap.clear();
				for(int serverId : serverIdListNow){
					String key = RedisKeys.arenaSeason + serverId;
					int season = Utils.intValue(Utils.getRedisStrValue(key)) + 1;
					RedisTools.set(EntityManager.redisClient, key, String.valueOf(season));
					addRobotJoMap(nextRobotJoMap.get(serverId), serverId, false);
				}
				nextRobotJoMap.clear();
			}
		}

	}

    private void dailtyServer() {

		long nowTime = Port.getTime();
		int maxNum = GlobalConfVal.getPvpTypeMaxNum(ParamKey.arenaPvpType_1);
        Map<Integer, RangeInt> snRangeMap = GlobalConfVal.getPvpRewardSnRangeMap(ParamKey.arenaPvpType_1);

		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN);
		long timeNext = Utils.formatTimeToLong(confGlobal.strValue);

		ConfGlobal conf = ConfGlobal.get("pvp_cross_sever");
		long time = Utils.formatTimeToLong(conf.strValue);
		if(time < nowTime){
			int serNoId = Utils.getServerIdTo(Config.SERVER_ID);
			if(conf.intArray[0] <= serNoId && serNoId <= conf.intArray[1]){
				// 跨服
				Log.temp.info("本服竞技场赛季已经结束，不发奖励， confSeason={}, timeNow={}, serverId {} -> {} timeNext={}, serverNoId={}",
						 confGlobal.value, nowTime,conf.intArray[0], conf.intArray[1], time, serNoId);
				return;
			}
		}
		Log.temp.info("===触发竞技场每日发奖励，{}, {}, serverIdListNow={}", Config.SERVER_ID, timeNext, serverIdListNow);
        for (int serverId : serverIdListNow) {
            if (serverDailyList.contains(serverId)) {
				Log.temp.info("===过滤竞技场每日发奖励，{}", serverId);
				continue;
			}
			serverDailyList.add(serverId);
			String key = ArenaManager.inst().getArenaRankKey(serverId, false);
			int season = Utils.intValue(Utils.getRedisStrValue(RedisKeys.arenaSeason + serverId));
			if(season < confGlobal.value || nowTime <= timeNext){
				sendReward(key, snRangeMap, maxNum, ParamKey.arenaDailtyMailSn, nowTime);
				Log.temp.info("===本服竞技场每日发奖励，serverId={}， key={}", serverId, key);
			}
			Log.temp.info("===本服竞技场，serverId={}， key={}， season={}， timeNext={}", serverId, key, confGlobal.value, timeNext);
		}
	}


	@ScheduleMethod(DataResetService.CRON_DAY_23_30ST)
	public void _CRON_DAY_23_30ST() {
		long timeNow = Port.getTime();
		// 发奖励
		sendSeasonReward();

	}

    private void dailtyBridgeServer() {

		int maxNum = GlobalConfVal.getPvpTypeMaxNum(3);
        Map<Integer, RangeInt> snRangeMap = GlobalConfVal.getPvpRewardSnRangeMap(3);
		long nowTime = Port.getTime();
		long timeWeekOne = Utils.getTimeOfWeek(nowTime, 1, 0);
		String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");

        for (int serverId : serverIdListNow) {
			String seasonKey = RedisKeys.arenaSeason + serverId;
			int season = Utils.intValue(Utils.getRedisStrValue(seasonKey));
            if (season <= ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN).value) {
				continue;
			}
			season += 1;
			RedisTools.set(EntityManager.redisClient, seasonKey, String.valueOf(season));

            if (serverDailyList.contains(serverId)) {
				continue;
			}
			serverDailyList.add(serverId);
			int groupIndex = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));

			int zone = GlobalConfVal.getZone(serverId);
			String key = RedisKeys.arenaBridgeList + Utils.getRedisStrValue(RedisKeys.admin_server_date_zone_group + dateStr + zone + groupIndex);
			sendReward(key, snRangeMap, maxNum, ParamKey.arenaDailtyMailSn, nowTime);
			Log.temp.info("===跨服竞技场每日发奖励，seson={}.serverId={}", season, serverId);
		}
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_7_ST_23_35)
    public void _CRON_WEEK_7_ST_23_35() {
        if (S.isBridge) {
			return;
		}
		long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0) + Time.WEEK;
		String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        for (int serverId : serverIdListNow) {
            ArenaManager.inst().getArenaRankKey(serverId, S.isBridge, true, res -> {
				String key = res.result();
				int zone = GlobalConfVal.getZone(serverId);
				ArenaManager.inst().initRobot(key, zone, serverId, dateStr, true, true);
				Log.temp.info("===初始化下周机器人，serverId={}, key={}, zone={}", serverId, key, zone);
			});
		}

	}

	@ScheduleMethod(DataResetService.CRON_WEEK_1_0)
	public void _CRON_WEEK_1_0() {
		long timeNow = Port.getTime();
		isClearServer();
	}

    private void isClearServer() {
        if (!S.isGameServer) {
			return;
		}
		List<String> keyList = new ArrayList<>();
        for (int serverId : serverIdListNow) {
			// 本服竞技场功能清
			int season = Utils.intValue(Utils.getRedisStrValue(RedisKeys.arenaSeason + serverId)) - 1;
			keyList.add(RedisKeys.arenaList + serverId + season);
			// 本服竞技场排行清
			String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeArena_1004, serverId);
			keyList.add(redisKey);
		}
        if (keyList.isEmpty()) {
			Log.temp.info("===无数据不处理，serverIdList = {}", serverIdListNow);
			return;
		}
        EntityManager.redisClient.del(keyList, h -> {
			if (h.succeeded()) {
				Log.temp.info("===清除竞技场周排行榜成功。keyList={}", keyList);
			}
		});
	}

    private void sendSeasonReward() {
        if (isSend) {
			Log.temp.error("===未过23:30点，状态未重置，不发奖励isSend={}", isSend);
			return;
		}
		isSend = true;

        if (S.isGameServer) {
			// 每日奖励
			dailtyServer();
			// 本服奖励
			serverSeasonReward();
		}
	}

    private void serverSeasonReward() {
		long timeNow = Port.getTime();
		int week = Utils.getDayOfWeek();// 周日1,周一2...周六7
		int maxNum = GlobalConfVal.getPvpTypeMaxNum(ParamKey.arenaPvpType_2);
        Map<Integer, RangeInt> snRangeMap = GlobalConfVal.getPvpRewardSnRangeMap(ParamKey.arenaPvpType_2);

		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN);
		long timeNext = Utils.formatTimeToLong(confGlobal.strValue);
		int seasonNow = Utils.intValue(Utils.getRedisStrValue(RedisKeys.arenaSeason + Config.SERVER_ID));
		if(seasonNow >= confGlobal.value && timeNow >= timeNext){
			Log.temp.info("本服竞技场赛季已经结束，不发奖励，season={}, confSeason={}, timeNow={}, timeNext={}",
					seasonNow, confGlobal.value, timeNow, timeNext);
			return;	// 赛季已结束
		}
		ConfGlobal conf = ConfGlobal.get("pvp_cross_sever");
		long time = Utils.formatTimeToLong(conf.strValue);
		if(time < timeNow){
			int serNoId = Utils.getServerIdTo(Config.SERVER_ID);
			if(conf.intArray[0] <= serNoId && serNoId <= conf.intArray[1]){
				Log.temp.info("本服竞技场赛季已经结束，不发奖励，season={}, confSeason={}, timeNow={}, serverId {} -> {} timeNext={}, serverNoId={}",
						seasonNow, confGlobal.value, timeNow,conf.intArray[0], conf.intArray[1], time, serNoId);
				// 跨服
				return;
			}
		}

        for (int serverId : serverIdListNow) {
			int day = Utils.getDaysBetween(timeNow, Util.getOpenServerTime(serverId)) + 1;
            if (day < GlobalConfVal.arenaDay) {
				continue;
			}

            if (day == GlobalConfVal.arenaDay) {
				// 记录第一赛季
				RedisTools.set(EntityManager.redisClient, RedisKeys.arenaSeason + serverId, "1");// 第1赛季结束
                if (serverSeasonList.contains(String.valueOf(serverId))) {
					continue;
				}
				serverSeasonList.add(String.valueOf(serverId));
				sendReward(ArenaManager.inst().getArenaRankKey(serverId, S.isBridge), snRangeMap, maxNum, ParamKey.arenaSeasonMailSn, timeNow);
				Log.temp.info("===本服第一赛季结束，serverId={}", serverId);
                if (!S.isBridge && !S.isGameLeagueOpen && !S.isAdmin) {

					scheduleOnce(new ScheduleTask() {
						@Override
						public void execute() {
							long timeWeekOne = Utils.getTimeOfWeek(Port.getTime() + Time.DAY, 1, 0);
							String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
							int zone = GlobalConfVal.getZone(serverId);
							ArenaManager.inst().initRobot(ArenaManager.inst().getArenaRankKey(serverId, S.isBridge), zone, serverId, dateStr);
						}
					}, Utils.random(600, 900) * Time.SEC);
				}
				continue;
			}
            if (week != 1) {// 不是周日
				continue;
			}
			String key = RedisKeys.arenaSeason + serverId;
			int season = Utils.intValue(Utils.getRedisStrValue(key)) + 1;
			sendReward(ArenaManager.inst().getArenaRankKey(serverId, S.isBridge), snRangeMap, maxNum, ParamKey.arenaSeasonMailSn, timeNow);
			Log.temp.info("===本服第{}赛季结束, 第二赛季后走统一，serverId={}", season, serverId);
		}
	}

    private void sendReward(String key, Map<Integer, RangeInt> snRangeMap, int maxNum, int mailSn, long timeNow) {
		List<String> strList = Utils.getRedisZaddStrValueList(key, 0, maxNum, false);
		int rank = 0;
		Param param = new Param();
		param.put(Mail.K.rtime, timeNow);
		Set<Long> idset = new HashSet<>();
        for (String idStr : strList) {
			++rank;
			long id = Utils.longValue(idStr);
            if (ArenaManager.inst().isRobot(id)) {
				continue;
			}
            if (idset.contains(id)) {
				continue;
			}
			idset.add(id);
			int confSn = 0;
            for (Map.Entry<Integer, RangeInt> entry : snRangeMap.entrySet()) {
				RangeInt rangeInt = entry.getValue();
                if (rangeInt.min <= rank && rank <= rangeInt.max) {
					confSn = entry.getKey();
					break;
				}
			}
			ConfPvpReward conf = ConfPvpReward.get(confSn);
            if (conf == null) {
				Log.temp.error("===ConfPvpReward找不到奖励配置，sn={}", confSn);
				continue;
			}
			JSONObject jo = new JSONObject();
			JSONObject joTemp = new JSONObject();
			joTemp.put(MailManager.MAIL_K_4, rank);
			jo.put(MailManager.MAIL_PARAM_1, joTemp);
			MailManager.inst().sendMail(id, MailManager.SYS_SENDER, mailSn, "", jo.toJSONString(), Utils.mapIntIntToJSON(conf.reward), param);
		}
	}

    private void bridgeSeasonReward() {
        if (!S.isBridge || S.isGameLeagueOpen) {
			return;
		}

		int week = Utils.getDayOfWeek();// 周日1,周一2...周六7
        if (week != 1 && week != 4) { // 周三或周日23:30赛季结算
			return;
		}
		long nowTime = Port.getTime();
		int maxNum = GlobalConfVal.getPvpTypeMaxNum(4);
        Map<Integer, RangeInt> snRangeMap = GlobalConfVal.getPvpRewardSnRangeMap(4);

		long timeWeekOne = Utils.getTimeOfWeek(nowTime, 1, 0);
		String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
		List<Integer> groupIndexList = new ArrayList<>();
        for (int serverId : serverIdListNow) {
			int season = Utils.intValue(Utils.getRedisStrValue(RedisKeys.arenaSeason + serverId));
            if (season <= ConfGlobal.get(ConfGlobalKey.pvp_cross_sever_time.SN).value) {
				continue;
			}
			int groupIndex = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));
            if (groupIndexList.contains(groupIndex)) {
				continue;
			}
			groupIndexList.add(groupIndex);
			int zone = GlobalConfVal.getZone(serverId);
			String key = RedisKeys.arenaBridgeList + Utils.getRedisStrValue(RedisKeys.admin_server_date_zone_group + dateStr + zone + groupIndex);
            if (serverSeasonList.contains(key)) {
				continue;
			}
			serverSeasonList.add(key);
			sendReward(key, snRangeMap, maxNum, ParamKey.arenaSeasonMailSn, nowTime);
			Log.temp.info("===跨服竞技场每日发奖励，season={}.serverId={}", season, serverId);
		}

	}


    private void test(String key, int serverId) {
	}


	@DistrMethod
	public void arenaBattleSettle(int serverId, long humanId, boolean isWin, Param param) {
		HumanBrief humanBriefMy = Utils.getParamValue(param, "humanBriefMy", null);
		if(humanBriefMy == null){
			Log.temp.error("===arenaBattleSettle humanId={} humanBriefMy == null", humanId);
			port.returns("result", false);
			return;
		}
		long rivalId = Utils.getParamValue(param, "rivalId", 0L);
		HumanBrief humanBriefRival = Utils.getParamValue(param, "humanBriefRival", null);
		long combatSeed = Utils.getParamValue(param, "combatSeed", 0L);
		if(combatSeed == 0){
			Log.temp.error("===arenaBattleSettle humanId={} combatSeed=0", humanId);
			port.returns("result", false);
			return;
		}
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.pvp_score_change_range.SN);
        if (confGlobal == null) {
			Log.temp.error("===ConfGlobal sn={}", ConfGlobalKey.pvp_score_change_range.SN);
			port.returns( "result", false);
			return;
		}
        if (confGlobal.intArray == null || confGlobal.intArray.length < 2) {
			Log.temp.error("===ConfGlobal sn={}", ConfGlobalKey.pvp_score_change_range.SN);
			port.returns( "result", false);
			return;
		}
		int addScoreWin = confGlobal.intArray[0];// 赢的加分
		int addScoreLose = confGlobal.intArray[1];// 输的加分

		String key = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);

		int rivalScore = ArenaManager.inst().getArenaRankScore(rivalId, key);
		int myScore = ArenaManager.inst().getArenaRankScore(humanId, key);
		if(myScore <= 0){
			myScore = ParamKey.initArenaScore;
		}
		if(rivalScore <= 0){
			rivalScore = ParamKey.initArenaScore;
		}

		int fillScore = 0;
        if (Math.abs(rivalScore - myScore) > ParamKey.arenaAbsScore) {
            fillScore = (int) ((rivalScore - myScore) / ParamKey.arenaAbsScore);
            if (fillScore > confGlobal.value) {
				fillScore = confGlobal.value;
            } else if (fillScore < -confGlobal.value) {
				fillScore = -confGlobal.value;
			}
		}
        if (!isWin) { // 失败反转一下正负
			fillScore = -fillScore;
		}
		int loseScore = addScoreLose - fillScore;
		int winScore = addScoreWin + fillScore;

		String rivalIdStr = String.valueOf(rivalId);
		String humanIdStr = String.valueOf(humanId);

		int change = 0;
		int rivalChange = 0;
        if (isWin) {
			// 自己胜利
			change = winScore;
			myScore += winScore;
            if (ArenaManager.inst().isRobot(rivalId)) {
				rivalChange = -1;
				rivalScore -= 1;
				// 机器人失败-1分
			} else {
				rivalChange = loseScore;
				rivalScore += loseScore;
			}
		} else {
			// 自己失败
			change = loseScore;
			myScore += loseScore;
			// 机器人
			rivalChange = winScore;
			rivalScore += winScore;
		}
		// 与策划约定，小于0后最低只能扣成1分
		if(myScore <= 0){
			myScore = 1;
		}
		if(rivalScore <= 0){
			rivalScore = 1;
		}
		RedisTools.addRank(EntityManager.redisClient, key, humanId, myScore);
		RedisTools.addRank(EntityManager.redisClient, key, rivalId, rivalScore);

		String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeArena_1004, serverId);
		// 本服排行榜
		RankManager.inst().rankUpdate(redisKey, String.valueOf(myScore), humanIdStr);

		int rank = Utils.getRedisRank(key, humanIdStr);
		int rivalRank = Utils.getRedisRank(key, rivalIdStr);

		Param param2 = new Param();
		param2.put("humanBriefMy", humanBriefMy);
        if (ArenaManager.inst().isRobot(rivalId)) {
			String json = robotJoMap.getOrDefault(serverId, new HashMap<>()).get(rivalId);
            if (json == null || json.isEmpty()) {
				String keyRobot = ArenaManager.inst().getRobotKey(serverId, rivalId, S.isBridge);
				json = Utils.getRedisStrValue(keyRobot);
			}
			param2.put("robotData", json);
			String rivalName = "";
			Define.p_head dHead = null;
            if (json != null) {
				JSONObject jo = Utils.toJSONObject(json);
				rivalName = jo.getString(ParamKey.nameKey);
				Define.p_head.Builder head = Define.p_head.newBuilder();
				head.setId(ParamKey.robotHeadID);
				head.setFrameId(0);
				head.setUrl("");
				dHead = head.build();
			}

			param2.put("myScore", myScore);
			param2.put("myChange", change);
			param2.put("rivalChange", rivalChange);
			param2.put("rivalScore", rivalScore);
			param2.put("serverId", serverId);
			ArenaManager.inst().createHistory(humanId, isWin, rivalId, ParamKey.historyType_arena, RedisKeys.arenaHumanHistoryList, combatSeed, param2, S.isBridge);
			port.returns("result", true,"rank", rank, "myScore", myScore, "change", change, "rivalChange", rivalChange,
					"rivalScore", rivalScore, "rivalRank", rivalRank, "rivalName", rivalName, "dHead", dHead);
		} else {
            if(humanBriefRival == null){
				Log.temp.error("===arenaBattleSettle humanId={} rivalId={} humanBriefRival == null", humanId, rivalId);
				port.returns("result", false);
				return;
			}
			String rivalName = humanBriefRival.getName();
			Define.p_head dHead = Define.p_head.newBuilder().setId(humanBriefRival.getHeadSn()).setFrameId(humanBriefRival.getCurrentHeadFrameSn()).setUrl("").build();
			param2.put("humanBriefRival", humanBriefRival);
			param2.put("myScore", myScore);
			param2.put("myChange", change);
			param2.put("rivalChange", rivalChange);
			param2.put("rivalScore", rivalScore);
			param2.put("serverId", serverId);
			ArenaManager.inst().createHistory(humanId, isWin, rivalId, ParamKey.historyType_arena, RedisKeys.arenaHumanHistoryList, combatSeed, param2, S.isBridge);
			port.returns("result", true,"rank", rank, "myScore", myScore, "change", change, "rivalChange", rivalChange,
					"rivalScore", rivalScore, "rivalRank", rivalRank, "rivalName", rivalName, "dHead", dHead);
		}
	}


	@DistrMethod
	public void update(String jo) {

	}

	@DistrMethod
	public void update1(Object... objs) {
		Log.temp.info("gm {}", Arrays.toString(objs));
		int type = Utils.intValue(objs[0]);
        if (type == 1) {
//			int serverId = Utils.intValue(objs[1]);
			isSend = false;
			serverDailyList.clear();
			serverSeasonList.clear();
			sendSeasonReward();
        } else if (type == 2) {
			Log.temp.error("===测试tcp链接， isBridge={}, isGameLeagueOpen={}, isAdmin={}", S.isBridge, S.isGameLeagueOpen, S.isAdmin);
			port.returns("ok");
			return;
		}
	}

	@DistrMethod
	public void update2(String str) {

	}

	@DistrMethod
    public void addRobot(Map<Long, Define.p_rank_info> robotMap, int serverId) {
		this.robotMap.put(serverId, robotMap);
		Log.temp.debug("===替换机器人数据, serverId={}, robotMapSize={}", serverId, robotMap.size());
	}

	@DistrMethod
    public void addRobotJoMap(Map<Long, String> robotJoMap, int serverId, boolean isNext) {
        if (robotJoMap == null) {
			return;
		}
        if (isNext) {
            this.nextRobotJoMap.put(serverId, robotJoMap);
		} else {
			Map<Long, Define.p_rank_info> robotMap = new HashMap<>();
            for (Map.Entry<Long, String> entry : robotJoMap.entrySet()) {
				JSONObject jo = Utils.toJSONObject(entry.getValue());
				robotMap.put(entry.getKey(), ArenaManager.inst().robot_p_rank_info(jo, 0, 0));
			}
			addRobot(robotMap, serverId);
            this.robotJoMap.put(serverId, robotJoMap);
		}

	}

	@DistrMethod
    public void getAreanRobotMap(int serverId) {

		port.returns("robotMap", robotMap.get(serverId));
	}

	@DistrMethod
    public void getAreanRobotJoMap(int serverId) {
		port.returns("robotJoMap", robotJoMap.get(serverId));
	}

	@DistrMethod
    public void getAreanRank(int serverId, long humanId, int page, boolean isRank) {
		ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeArena_1004);
		String redisKey = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);
    
		long pid = port.createReturnAsync();
		RedisTools.getRankLen(EntityManager.getRedisClient(), redisKey, rest -> {
			if (rest.failed()) {
                Log.temp.error("获取失败，redisKey={}", redisKey);
				return;
			}
			long total = rest.result();
			int min = (page - 1) * RankParamKey.arenaPageNum;
			int max = page * RankParamKey.arenaPageNum - 1;
            if (!isRank || max > confRanktype.show_num - 1) {
				max = confRanktype.show_num - 1;
			}

			RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, min, max, true, ret -> {
                if (ret.failed()) {
					Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
					return;
				}
				JsonArray json = ret.result();
				int size = json.getList().size();
				int num = size / 2;
				List<Long> idRankList = new ArrayList<>(num);
				List<Integer> scoreRankList = new ArrayList<>(num);
				List<Long> humanIdList = new ArrayList<>();
                for (int i = 0; i < size; i += 2) {
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i + 1));
					idRankList.add(id);
					scoreRankList.add(score);
                    if (!ArenaManager.inst().isRobot(id)) {
						humanIdList.add(id);
					}
				}

				MsgArena.arena_rank_list_s2c.Builder msgArena = MsgArena.arena_rank_list_s2c.newBuilder();
				Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
                if (isRank) {
					rankData.setServer(Utils.getServerIdTo(serverId));
					rankData.setType(RankParamKey.rankTypeArena_1004);
					rankData.setPage(page);
					rankData.setTotalNum(Utils.intValue(total));
                    rankData.setMaxPage((int) Math.ceil(total / (double) RankParamKey.arenaPageNum));
					rankData.setNextFreshTime(0);
				} else {
					int endTime = ArenaManager.inst().calculateEndTime(serverId, S.isBridge);
					msgArena.setEndTime(endTime);
					msgArena.setStage(Utils.getTimeSec() < endTime ? 1 : 0);// 先觉得正常能否挑战
                    if (Utils.getTimeSec() > endTime) {// 本服的第一赛季结束后第二赛季第二天就开始了，所以要判断一下
                        int endTimeWeek_7 = (int) (Utils.getTimeOfWeek(Port.getTime(), 7, ParamKey.arenaHour23) / Time.SEC);
                        if (Utils.isSameDay(Port.getTime(), endTime * Time.SEC)) {
                            endTime = (int) (Utils.getOffDayTime(Port.getTime(), 1, 0) / Time.SEC);
						} else {
							msgArena.setStage(Utils.getTimeSec() < endTimeWeek_7 ? 1 : 0);// 因特殊所以重新判断能否挑战
							endTime = endTimeWeek_7;
						}
						msgArena.setEndTime(endTime);
					}
					msgArena.setIsCross(ParamKey.sourceKey_0);
				}
				// 没有真人
                if (humanIdList.isEmpty()) {
					int rank = min;
					Map<Long, Define.p_rank_info> robotRankInfoMap = this.robotMap.get(serverId);
					Map<Long, String> robotArenaMap = this.robotJoMap.get(serverId);
                    if (robotRankInfoMap == null) {
						initLoadRobot(serverId);
						return;
					}

					// TODO 机器人加自己
                    for (int i = 0; i < num; i++) {
						rank++;
						long id = idRankList.get(i);
						int score = scoreRankList.get(i);
                        if (isRank) {
							Define.p_rank_info rankInfo = robotRankInfoMap.get(id);
                            if (rankInfo == null) {
								continue;
							}
							rankData.addRankInfo(rankInfo.toBuilder().setRank(rank).setScore(score).build());
						} else {
                            if (robotArenaMap == null) {
								continue;
							}
							String jsonStr = robotArenaMap.get(id);
							if (jsonStr == null || jsonStr.isEmpty()) {
								continue;
							}
							JSONObject jo = Utils.toJSONObject(jsonStr);
							msgArena.addRankList(ArenaManager.inst().robot_to_p_arena_rank(jo, rank, score));
						}
					}
					// 同步改异步，同步获取会导致失败，整个排行榜显示没了
                    getMyRankScore(pid, redisKey, serverId, humanId, page, isRank, msgArena, rankData, (int) total);
					return;
				}

				// 机器人排行榜+玩家
                HumanData.getList(humanIdList, HumanManager.inst().humanClasses, res -> {
                    if (!res.succeeded()) {
						Log.human.error("MemberCallback getMemberAsync error", res.cause());
						return;
					}
					List<HumanData> humanDataList = res.result();
					int rank = min;
					boolean isNotMy = true;
					Map<Long, HumanData> humanDataMap = new HashMap<>();
                    for (HumanData humanData : humanDataList) {
						humanDataMap.put(humanData.human.getId(), humanData);
					}
					humanDataList.clear();

					Map<Long, Define.p_rank_info> robotRankInfoMap = this.robotMap.get(serverId);
					Map<Long, String> robotArenaMap = this.robotJoMap.get(serverId);

                    for (int i = 0; i < num; i++) {
						rank++;
						long id = idRankList.get(i);
						int score = scoreRankList.get(i);
                        if (ArenaManager.inst().isRobot(id)) {// 机器人
                            if (isRank) {
								Define.p_rank_info rankInfo = robotRankInfoMap.get(id);
								rankData.addRankInfo(rankInfo.toBuilder().setRank(rank).setScore(score).build());
							} else {
								String jsonStr = robotArenaMap.get(id);
								if (jsonStr == null || jsonStr.isEmpty()) {
									continue;
								}
								JSONObject jo = Utils.toJSONObject(jsonStr);
								msgArena.addRankList(ArenaManager.inst().robot_to_p_arena_rank(jo, rank, score));
							}
						} else {// 玩家
							HumanData humanData = humanDataMap.get(id);
                            if (humanData == null) {
								continue;
							}
                            if (isRank) {
								Define.p_rank_info.Builder rankInfo = new RankInfo(humanData.human, humanData.human2, score).rank_info.toBuilder();
								rankInfo.setRank(rank);
								rankData.addRankInfo(rankInfo);
                                if (humanId == id) {
									rankData.setMyRankInfo(rankInfo);
									isNotMy = false;
								}
							} else {
                                if (humanId == id) {
									msgArena.setMyRank(rank);
									msgArena.setMyScore(score);
									isNotMy = false;
								}
								msgArena.addRankList(ArenaManager.inst().to_p_arena_rank(humanData, rank, score, serverId, ParamKey.sourceKey_0));
							}
						}
					}
                    if (!isNotMy) {
                        if (isRank) {
							port.returnsAsync(pid, "msgRank", rankData.build(), "isRank", isRank, "isNot", true);
						} else {
                            port.returnsAsync(pid, "msgArena", msgArena.build(), "isRank", isRank, "isNot", true);
						}
						return;
					}
                    getMyRankScore(pid, redisKey, serverId, humanId, page, isRank, msgArena, rankData, (int) total);
				});
			});
		});

	}

	private void getMyRankScore(long pid, String redisKey, int serverId, long humanId, int page, boolean isRank,
                                MsgArena.arena_rank_list_s2c.Builder msgArena, Define.p_rank_data.Builder rankData, int total) {
		RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanId), ret2 -> {
            if (ret2.failed()) {
				Log.temp.error("获取排行榜失败, rankSn={}, humanId={}, 排名获取失败，默认最后一名，total={}", RankParamKey.rankTypeArena_1004, humanId, total);
                if (isRank) {
					port.returnsAsync(pid, "msgRank", rankData.build(), "isRank", isRank, "myScore", ParamKey.initArenaScore, "myRank", total);
				} else {
                    msgArena.setMyRank((int) total);
					msgArena.setMyScore(ParamKey.initArenaScore);
                    port.returnsAsync(pid, "msgArena", msgArena.build(), "isRank", isRank, "myScore", ParamKey.initArenaScore, "myRank", total);
				}
			} else {
				int myRank = Utils.intValue(ret2.result());
				RedisTools.getMyScore(EntityManager.redisClient, redisKey, humanId, ret3 -> {
                    if (ret3.failed()) {
						Log.temp.error("获取排行榜失败, rankSn={}, humanId={}, rank={}, 积分获取失败", RankParamKey.rankTypeArena_1004, humanId, myRank);
                        if (isRank) {
							port.returnsAsync(pid, "msgRank", rankData.build(), "isRank", isRank, "myScore", ParamKey.initArenaScore, "myRank", myRank);
						} else {
							msgArena.setMyRank(myRank);
							msgArena.setMyScore(ParamKey.initArenaScore);
                            port.returnsAsync(pid, "msgArena", msgArena.build(), "isRank", isRank, "myScore", ParamKey.initArenaScore, "myRank", myRank);
						}
						return;
					}
					int myScore = Utils.intValue(ret3.result());
                    if (myScore == 0) {
						myScore = ParamKey.initArenaScore;
					}
                    if (isRank) {
						port.returnsAsync(pid, "msgRank", rankData.build(), "isRank", isRank, "myScore", myScore, "myRank", myRank);
					} else {
						msgArena.setMyRank(myRank);
						msgArena.setMyScore(myScore);
                        port.returnsAsync(pid, "msgArena", msgArena.build(), "isRank", isRank, "myScore", myScore, "myRank", myRank);
					}
				});
			}
		});
	}


	@DistrMethod
	public void getAreanRankOld(int serverId, long humanId) {
		ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeArena_1004);
		 ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);
		MsgArena.arena_rank_list_s2c.Builder msgArena = MsgArena.arena_rank_list_s2c.newBuilder();
		msgArena.setIsCross(ParamKey.sourceKey_0);
		msgArena.setIsOld(true);
		int season = Utils.intValue(Utils.getRedisStrValue(RedisKeys.arenaSeason + serverId)) - 1;
		if(season < 0){
			port.returns("msg", msgArena.build());
			return;
		}
		// 本服竞技场
		String redisKey = RedisKeys.arenaList + serverId + season;

		long pid = port.createReturnAsync();
		RedisTools.getRankLen(EntityManager.getRedisClient(), redisKey, rest -> {
			if (rest.failed()) {
				Log.temp.error("获取失败，redisKey={}", redisKey);
				port.returnsAsync(pid, "msg", msgArena.build());
				return;
			}
			long total = rest.result();
			int max = confRanktype.show_num - 1;

			RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, 0, max, true, ret -> {
				if (ret.failed()) {
					Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
					port.returnsAsync(pid, "msg", msgArena.build());
					return;
				}
				JsonArray json = ret.result();
				int size = json.getList().size();
				int num = size / 2;
				List<Long> idRankList = new ArrayList<>(num);
				List<Integer> scoreRankList = new ArrayList<>(num);
				List<Long> humanIdList = new ArrayList<>();
				for (int i = 0; i < size; i += 2) {
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i + 1));
					idRankList.add(id);
					scoreRankList.add(score);
					if (!ArenaManager.inst().isRobot(id)) {
						humanIdList.add(id);
					}
				}

				// 没有真人
				if (humanIdList.isEmpty()) {
					int rank = 0;
					Map<Long, Define.p_rank_info> robotRankInfoMap = this.robotMap.get(serverId);
					Map<Long, String> robotArenaMap = this.robotJoMap.get(serverId);
					if (robotRankInfoMap == null) {
						Log.temp.error("robotRankInfoMap is null, serverId={}", serverId);
						port.returnsAsync(pid, "msg", msgArena.build());
						return;
					}
					// 机器人和自己
					for (int i = 0; i < num; i++) {
						rank++;
						long id = idRankList.get(i);
						int score = scoreRankList.get(i);
						if (robotArenaMap == null) {
							continue;
						}
						String jsonStr = robotArenaMap.get(id);
						if (jsonStr == null || jsonStr.isEmpty()) {
							continue;
						}
						JSONObject jo = Utils.toJSONObject(jsonStr);
						msgArena.addRankList(ArenaManager.inst().robot_to_p_arena_rank(jo, rank, score));
					}
					// 同步改异步，同步获取会导致失败，整个排行榜显示没了
					getMyRankScoreOldRank(pid, redisKey, humanId, msgArena, (int) total);
					return;
				}

				// 机器人排行榜+玩家
				CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, res -> {
					if (!res.succeeded()) {
						Log.human.error("MemberCallback getMemberAsync error", res.cause());
						port.returnsAsync(pid, "msg", msgArena.build());
						return;
					}
					List<HumanBrief> humanBriefList = res.result();
					int rank = 0;
					boolean isNotMy = true;
					int selectNum = humanIdList.size();
					Map<Long, HumanBrief> humanBriefMap = new HashMap<>();

					List<Long> loadId = new ArrayList<>();
					int index = -1;
					for (HumanBrief humanBrief : humanBriefList) {
						index++;
						if(humanBrief == null){
							if(index >= 0 && index < selectNum){
								loadId.add(humanIdList.get(index));
							}
							continue;
						}
						humanBriefMap.put(humanBrief.getId(), humanBrief);
					}
					humanBriefList.clear();
					Map<Long, String> robotArenaMap = this.robotJoMap.get(serverId);

					for (int i = 0; i < num; i++) {
						rank++;
						long id = idRankList.get(i);
						int score = scoreRankList.get(i);
						if (ArenaManager.inst().isRobot(id)) {// 机器人
							String jsonStr = robotArenaMap.get(id);
							if (jsonStr == null || jsonStr.isEmpty()) {
								continue;
							}
							JSONObject jo = Utils.toJSONObject(jsonStr);
							msgArena.addRankList(ArenaManager.inst().robot_to_p_arena_rank(jo, rank, score));
						} else {// 玩家
							HumanBrief humanBrief = humanBriefMap.get(id);
							if (humanBrief == null) {
								loadId.add(id);
								continue;
							}
							if (humanId == id) {
								msgArena.setMyRank(rank);
								msgArena.setMyScore(score);
								isNotMy = false;
							}
							msgArena.addRankList(ArenaManager.inst().to_p_arena_rank(humanBrief, rank, score, serverId, ParamKey.sourceKey_0));
						}
					}
					if(!loadId.isEmpty()){
						EntityManager.batchGetEntity(HumanBrief.class, loadId, res3 -> {});
					}

					if (!isNotMy) {
						port.returnsAsync(pid, "msg", msgArena.build());
						return;
					}
					getMyRankScoreOldRank(pid, redisKey, humanId, msgArena, (int) total);
				});
			});
		});

	}

	private void getMyRankScoreOldRank(long pid, String redisKey, long humanId, MsgArena.arena_rank_list_s2c.Builder msgArena, int total) {
		RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanId), ret2 -> {
			if (ret2.failed()) {
				Log.temp.error("获取排行榜失败, rankSn={}, humanId={}, 排名获取失败，默认最后一名，total={}", RankParamKey.rankTypeArena_1004, humanId, total);
				msgArena.setMyRank((int) total + 1);
				msgArena.setMyScore(ParamKey.initArenaScore);
				port.returnsAsync(pid, "msg", msgArena.build());
			} else {
				int myRank = Utils.intValue(ret2.result());
				RedisTools.getMyScore(EntityManager.redisClient, redisKey, humanId, ret3 -> {
					if (ret3.failed()) {
						Log.temp.error("获取排行榜失败, rankSn={}, humanId={}, rank={}, 积分获取失败", RankParamKey.rankTypeArena_1004, humanId, myRank);
						msgArena.setMyRank(myRank);
						msgArena.setMyScore(ParamKey.initArenaScore);
						port.returnsAsync(pid, "msg", msgArena.build());
						return;
					}
					int myScore = Utils.intValue(ret3.result());
					if (myScore == 0) {
						myScore = ParamKey.initArenaScore;
					}
					msgArena.setMyRank(myRank);
					msgArena.setMyScore(myScore);
					port.returnsAsync(pid, "msg", msgArena.build());
				});
			}
		});
	}
}