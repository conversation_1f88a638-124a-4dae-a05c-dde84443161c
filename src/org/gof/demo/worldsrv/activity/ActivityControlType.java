package org.gof.demo.worldsrv.activity;


/***
 * 运营活动表类型
 */
public class ActivityControlType {
    public static final int Act_5 = 5;     //咕咕鸡
    public static final int Act_6 = 6;     //关卡冲榜
    public static final int Act_7 = 7;     //同伴冲榜
    public static final int Act_8 = 8;     //技能冲榜
    public static final int Act_9 = 9;     //坐骑冲榜
    public static final int Act_10 = 10;   //家族祈灵
    public static final int Act_11 = 11;   //祈灵冲刺
    public static final int Act_12 = 12;   //科技冲刺
    public static final int Act_13 = 13;   //遗物冲刺

    public static final int Act_14 = 14;     //武魂冲刺
    public static final int Act_15 = 15;     //咕咕鸡
    public static final int Act_16 = 16;     //咕咕鸡
    public static final int Act_17 = 17;     //咕咕鸡

    public static final int Act_20 = 20;     //乱斗
    public static final int Act_23 = 23;     //七夕战令
    public static final int Act_25 = 25;     //七夕送礼（包含送花榜）
    public static final int Act_26 = 26;    // 收花榜

    public static final int Act_28 = 28;     //战令
    public static final int Act_33 = 33;     //跨服战
    public static final int Act_44 = 44;     //农场战令
    public static final int Act_52 = 52;    // 三消小游戏
    public static final int Act_56 = 56;     //七日试炼
    public static final int Act_57 = 57;     //七日试炼
    public static final int Act_58 = 58;     //七日试炼
    public static final int Act_59 = 59;     //七日试炼
    public static final int Act_60 = 60;     //七日试炼
    public static final int Act_61 = 61;     //七日试炼
    public static final int Act_62 = 62;     //七日试炼
    public static final int Act_64 = 64;     //打地鼠
    public static final int Act_69 = 69;     //招财猫
    public static final int Act_72 = 72;     //勇者试炼
    public static final int Act_73 = 73;     //蜘蛛子夺宝
    public static final int Act_74 = 74;     //跨服车位
    public static final int Act_84 = 84;     //成长之路
    public static final int Act_85 = 85;     //成长之路
    public static final int Act_86 = 86;     //成长之路
    public static final int Act_87 = 87;     //成长之路
    public static final int Act_88 = 88;     //成长之路
    public static final int Act_89 = 89;     //成长之路
    public static final int Act_90 = 90;     //成长之路
    public static final int Act_166 = 166;     //滑梯转盘
    public static final int Act_167 = 167;     //鸭鸭海滨之旅,战令
    public static final int Act_168 = 168;     //中秋搜寻
    public static final int Act_169 = 169;     //miniGame
    public static final int Act_170 = 170;     //砸金蛋
    public static final int Act_171 = 171;    //登入奖励
    public static final int Act_1166 = 1166;    //滑梯转盘
    public static final int Act_1167 = 1167;    //鸭鸭海滨之旅,战令
    public static final int Act_1168 = 1168;    //中秋搜寻
    public static final int Act_1169 = 1169;    //miniGame
    public static final int Act_1170 = 1170;    //砸金蛋
    public static final int Act_1171 = 1171;    //登入奖励

    public static final int Act_1200 = 1200;    // 合服派对
    public static final int Act_1201 = 1201;    // 合服签到
    public static final int Act_1202 = 1202;    // 许愿转盘
    public static final int Act_1203 = 1203;    // 合服战令
    public static final int Act_1204 = 1204;    // 狂欢庆典
    public static final int Act_1205 = 1205;    // 交友会
    public static final int Act_1206 = 1206;    // 合服团购
    public static final int Act_1207 = 1207;    // 连充福利
    public static final int Act_1208 = 1208;    // 家族协力

    public static final int Act_2003 = 2003;    // 超值卡
    public static final int Act_2004 = 2004;    // 每日充值
    public static final int Act_2008 = 2008;    // 多档刷新礼包
    public static final int Act_2012 = 2012;    //SNS分享活动1
    public static final int Act_2013 = 2013;    //SNS分享活动2
    public static final int Act_2014 = 2014;    //SNS分享活动3
    public static final int Act_2015 = 2015;    //SNS分享活动4
    public static final int Act_2052 = 2052;    //星将战令
    public static final int Act_2053 = 2053;    //星将战令
    public static final int Act_2100 = 2100;    //Battle战令
    public static final int Act_2101 = 2101;    //战令
    public static final int Act_2166 = 2166;    //滑梯转盘
    public static final int Act_2167 = 2167;    //鸭鸭海滨之旅,战令
    public static final int Act_2168 = 2168;    //中秋搜寻
    public static final int Act_2169 = 2169;    //miniGame
    public static final int Act_2170 = 2170;    //砸金蛋
    public static final int Act_2171 = 2171;    //登入奖励

    public static final int Act_2201 = 2201;    // 钓鱼助力（空活动）
    public static final int Act_2202 = 2202;    // 钓鱼任务（空活动，为了挂活动任务）
    public static final int Act_2203 = 2203;    // 钓鱼签到
    public static final int Act_2204 = 2204;    // 钓鱼首充（空活动，为了挂礼包）
    public static final int Act_2205 = 2205;    // 钓鱼冲刺
    public static final int Act_2206 = 2206;    // 钓鱼累计
    public static final int Act_2207 = 2207;    // 钓鱼礼包

    public static final int Act_2301 = 2301;
    public static final int Act_3002 = 3002;    //登入奖励
    public static final int Act_3003 = 3003;    //转盘
    public static final int Act_3010 = 3010;    // 浇灌爱情树
    public static final int Act_3011 = 3011;    // 爱情花战令
    public static final int Act_3012 = 3012;    // 限时礼盒1
    public static final int Act_3013 = 3013;    // 限时礼盒2

    public static final int Act_3166 = 3166;    //魔王转盘
    public static final int Act_3167 = 3167;    //魔王战令
    public static final int Act_3168 = 3168;    //魔王搜寻
    public static final int Act_3169 = 3169;    //魔王miniGame
    public static final int Act_3170 = 3170;    //魔王砸金蛋
    public static final int Act_3171 = 3171;    //魔王登入奖励
    //圣诞一期
    public static final int Act_3265 = 3265;    // 圣诞1期
    public static final int Act_3266 = 3266;    // 圣诞1期轮盘
    public static final int Act_3267 = 3267;    // 圣诞1期通关
    public static final int Act_3268 = 3268;    // 圣诞献礼（给加成打pvp）
    public static final int Act_3269 = 3269;    // 圣诞1期射击游戏
    public static final int Act_3270 = 3270;    // 圣诞1期砸金蛋
    public static final int Act_3271 = 3271;    // 圣诞1期登入奖励

    //圣诞二期
    public static final int Act_3365 = 3365;    // 圣诞2期
    public static final int Act_3366 = 3366;    // 圣诞2期轮盘
    public static final int Act_3367 = 3367;    // 圣诞2期通关
    public static final int Act_3368 = 3368;    // 圣诞2期寻找
    public static final int Act_3369 = 3369;    // 圣诞2期射击游戏
    public static final int Act_3370 = 3370;    // 圣诞2期砸金蛋
    public static final int Act_3371 = 3371;    // 圣诞2期登入奖励

    //新年活动
    public static final int Act_3465 = 3465;    // 新年活动
    public static final int Act_3466 = 3466;    // 新年轮盘
    public static final int Act_3467 = 3467;    // 新年通关
    public static final int Act_3468 = 3468;    // 新年寻找
    public static final int Act_3469 = 3469;    // 新年射击游戏
    public static final int Act_3470 = 3470;    // 新年砸金蛋
    public static final int Act_3471 = 3471;    // 新年登入奖励
    public static final int Act_3473 = 3473;      // 全民赶年兽

    public static final int Act_3566 = 3566;    // 新年轮盘
    public static final int Act_3567 = 3567;    // 新年战令
    public static final int Act_3568 = 3568;    // 新年献礼
    public static final int Act_3569 = 3569;    // 新年射击游戏
    public static final int Act_3570 = 3570;    // 新年盛宴
    public static final int Act_3571 = 3571;    // 新年7日登陆

    public static final int Act_3665 = 3665;    // 冰河盛典
    public static final int Act_3666 = 3666;    // 冰河转盘
    public static final int Act_3667 = 3667;    // 冰河战令
    public static final int Act_3668 = 3668;    // 冰河献礼
    public static final int Act_3669 = 3669;    // 冰雕守卫战（三消）
    public static final int Act_3670 = 3670;    // 冰河盛宴
    public static final int Act_3671 = 3671;    // 冰河七日
    public static final int Act_3672 = 3672;    // 冰河应援

    public static final int Act_3765 = 3765;    // 闪耀甜心
    public static final int Act_3766 = 3766;    // 秘密花园
    public static final int Act_3767 = 3767;    // 甜心战令
    public static final int Act_3769 = 3769;    // 爱情保卫战（打地鼠）
    public static final int Act_3770 = 3770;    // 告白气球（燃冬盛宴）
    public static final int Act_3771 = 3771;    // 七日之约
    public static final int Act_3772 = 3772;    // 应援礼盒
    public static final int Act_3773 = 3773;    // 倾心榜
    public static final int Act_3774 = 3774;    // 情意榜
    public static final int Act_3775 = 3775;    // 跨服倾心榜
    public static final int Act_3776 = 3776;    // 跨服情意榜

    public static final int Act_3865 = 3865;    // 春日活动
    public static final int Act_3866 = 3866;    // 春日转盘
    public static final int Act_3867 = 3867;    // 春日战令
    public static final int Act_3868 = 3868;    // 春日线索
    public static final int Act_3869 = 3869;    // 春日打飞机
    public static final int Act_3870 = 3870;    // 春日砸金蛋
    public static final int Act_3871 = 3871;    // 春日登入

    public static final int Act_3965 = 3965;    // 百魔夜行
    public static final int Act_3966 = 3966;    // 百魔夜行1期结榜
    public static final int Act_3967 = 3967;    // 百魔夜行2期结榜
    public static final int Act_3968 = 3968;    // 百魔夜行战令
    public static final int Act_4000 = 4000;    // 签到
    public static final int Act_4066 = 4066;    // 转盘
    public static final int Act_4067 = 4067;    // 战令
    public static final int Act_4068 = 4068;    // 线索
    public static final int Act_4069 = 4069;    // 套圈
    public static final int Act_4070 = 4070;    // 大富翁
    public static final int Act_4071 = 4071;    // 签到
    public static final int Act_4001 = 4001;    // 登入
    public static final int Act_4072 = 4072;    // 礼盒
    public static final int Act_4165 = 4165;    // 史莱姆活动
    public static final int Act_4166 = 4166;    // 转盘
    public static final int Act_4167 = 4167;    // 战令
    public static final int Act_4168 = 4168;    // 线索
    public static final int Act_4169 = 4169;    // 地鼠
    public static final int Act_4170 = 4170;    // 宝盒
    public static final int Act_4265 = 4265;    // 史莱姆活动
    public static final int Act_4266 = 4266;    // 转盘
    public static final int Act_4267 = 4267;    // 战令
    public static final int Act_4268 = 4268;    // 线索
    public static final int Act_4269 = 4269;    // 吃豆豆
    public static final int Act_4270 = 4270;    // 宝盒
    public static final int Act_4300 = 4300;    // 卡皮巴拉玩法
    public static final int Act_4301 = 4301;    // 联动预热活动
    public static final int Act_4373 = 4373;    // 联动全民打boss
    public static final int Act_4302 = 4302;    // 联动史莱姆融合
    public static final int Act_4303 = 4303;    // 联动大富翁
    public static final int Act_4304 = 4304;    // 联动签到
    public static final int Act_4305 = 4305;    // 复制排行榜
    public static final int Act_4306 = 4306;    // 复制排行榜

    public static final int Act_4466 = 4466;    // 转盘
    public static final int Act_4467 = 4467;    // 战令
    public static final int Act_4468 = 4468;    // 线索
    public static final int Act_4469 = 4469;    // 吃豆豆
    public static final int Act_4470 = 4470;    // 宝盒
    public static final int Act_4471 = 4471;    // 登入奖励

    public static final int Act_4566 = 4566;    // 滑梯转盘
    public static final int Act_4567 = 4567;    // 鸭鸭海滨之旅,战令
    public static final int Act_4568 = 4568;    // 中秋搜寻
    public static final int Act_4569 = 4569;    // 三消
    public static final int Act_4570 = 4570;    // 黄金塔
    public static final int Act_4571 = 4571;    // 登入奖励

    public static final int Act_4665 = 4665;    // 大战魔王城
    public static final int Act_4666 = 4666;    // 大战魔王城复制榜
    public static final int Act_4667 = 4667;    // 大战魔王城复制榜
    public static final int Act_4668 = 4668;    // 战领

	public static final int Act_4771 = 4771;    // 转生魔剑
    public static final int Act_4772 = 4772;    // 转生魔剑短期榜1
    public static final int Act_4773 = 4773;    // 转生魔剑短期榜2
    public static final int Act_4774 = 4774;    // 转剑预热_阵营对抗
    public static final int Act_4775 = 4775;    // 转剑打造
    public static final int Act_4776 = 4776;    // 转剑预热-大富翁
    public static final int Act_4777 = 4777;    // 转剑预热-签到
    public static final int Act_4778 = 4778;    // 转剑预热-皮肤试用
    public static final int Act_4780 = 4780;    // 转生魔剑战令

    public static final int Act_5011 = 5011;    // 翻牌活动
    public static final int Act_5012 = 5012;    // 翻牌活动-战令
    public static final int Act_5013 = 5013;    // 合成大西瓜

    public static final int Act_4865 = 4865;    // 联动主题活动
    public static final int Act_4866 = 4866;    // 转剑转盘
    public static final int Act_4867 = 4867;    // 转剑战令
    public static final int Act_4868 = 4868;    // 转剑pvp
    public static final int Act_4869 = 4869;    // 转剑射箭
    public static final int Act_4870 = 4870;    // 转剑宝盒

    public static final int Act_4965 = 4965;    // 联动2期主活动
    public static final int Act_4966 = 4966;    // 转剑转盘
    public static final int Act_4967 = 4967;    // 转剑战令
    public static final int Act_4968 = 4968;    // 转剑线索
    public static final int Act_4969 = 4969;    // 转剑三消
    public static final int Act_4970 = 4970;    // 转剑叠叠乐

    // 周年庆典活动
    public static final int Act_5050 = 5050;    // 周年庆典
    public static final int Act_5051 = 5051;    // 周年庆典签到
    public static final int Act_5052 = 5052;    // 周年庆典限时档位礼包
    public static final int Act_5053 = 5053;    // 周年庆典福签抽奖
    public static final int Act_5054 = 5054;    // 周年庆典集换字
    public static final int Act_5055 = 5055;    // 周年庆典累天消费
    public static final int Act_5056 = 5056;    // 周年庆典累计消费
    public static final int Act_5065 = 5065;    // 周年庆1期
    public static final int Act_5066 = 5066;    // 周年庆1期转盘
    public static final int Act_5067 = 5067;    // 周年庆1期战令
    public static final int Act_5068 = 5068;    // 周年庆1期线索
    public static final int Act_5069 = 5069;    // 周年庆1期弹弓小游戏
    public static final int Act_5070 = 5070;    // 周年庆1期燃冬盛宴
    public static final int Act_5165 = 5165;    // 周年庆2期
    public static final int Act_5166 = 5166;    // 周年庆2期自选转盘
    public static final int Act_5167 = 5167;    // 周年庆2期战令
    public static final int Act_5168 = 5168;    // 周年庆2期周年献礼
    public static final int Act_5169 = 5169;    // 周年庆2期射箭小游戏
    public static final int Act_5170 = 5170;    // 周年庆2期叠叠乐

    public static final int Act_8001 = 8001;    // 空投礼包

    public static final int Act_8005 = 8005;    // 黑市活动
}
