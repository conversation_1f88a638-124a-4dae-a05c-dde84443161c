package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import org.apache.commons.collections4.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.*;
import org.gof.demo.support.DataLatestUtil;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfFarmGreens;
import org.gof.demo.worldsrv.config.ConfFarmLevel;
import org.gof.demo.worldsrv.entity.Farm;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;

import java.util.*;

/**
 * @program: game2
 * @description: 农场服务
 * @create: 2024-05-15 21:31
 **/
@DistrClass(servId = D.SERV_FARM, importClass = {Farm.class,List.class})
public class FarmService extends GameServiceBase {

    /** 有定时任务的农场数据**/
    private PriorityQueue<FarmVo> farmQueue = new PriorityQueue<>();
    private Map<Long, FarmVo> farmQueueMap = new HashMap<>();
    DataLatestUtil<Farm> farmLatest = new DataLatestUtil<>();
    List<Integer> serverIdListNow = new ArrayList<>();
    private Map<Long,FarmVo> currentPulseCheckFarms = new HashMap<>();
    private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理


    public FarmService(GamePort port) {
        super(port);
    }

    @Override
    protected void init() {
        checkServerId();
    }

    @DistrMethod
    public void loadServer(List<Integer> serverIds){
        //取serverList里面没有的serverIds的serverId，加到serverList里面
        for (Integer serverId : serverIds) {
            if(!serverIdListNow.contains(serverId)){
                continue;
            }
            serverIdListNow.add(serverId);
            loadFarm(serverId);
        }
    }

    private void loadFarm(int serverId){
        String redisKey = Utils.createStr("{}.{}", RedisKeys.farmInTimer, serverId);
        Utils.getRedisSet(redisKey, res->{
            List<String> farmStrIds =  res.result();
            if(farmStrIds == null||farmStrIds.isEmpty()){
                return;
            }
            Log.farm.info("批量查询加载农场FarmService:loadFarm,size={}",farmStrIds.size());
            List<Long> farmIds = new ArrayList<>(farmStrIds.size());
            for (String farmId : farmStrIds) {
                farmIds.add(Utils.longValue(farmId));
            }
            EntityManager.batchGetEntity(Farm.class, farmIds, batchRes -> {
                List<Farm> farms = batchRes.result();
                if(farms == null || farms.isEmpty()){
                   return;
                }
                for(Farm farm:farms) {
                    FarmVo farmVo = new FarmVo(farm);
                    if(farmVo.nextCheckTime != Long.MAX_VALUE){
                        addFarmVo(farmVo.id, farmVo);
                    }else{
                        RedisTools.delToSet(EntityManager.getRedisClient(),Utils.getRedisKey(farmVo.id, RedisKeys.farmInTimer), Long.toString(farmVo.id));
                    }
                }
            });
        });
    }

    @Override
    public void pulseOverride() {
        if (!farmQueue.isEmpty()) {
            FarmVo farmVo = farmQueue.peek();
            if ((farmVo.nextCheckTime < Port.getTime())) {
                farmQueue.poll();
                farmQueueMap.remove(farmVo.id);
                checkLandAndBuildingState(farmVo, res->{
                    boolean hasGrowingOrStealingLand = farmVo.landMap.values().stream()
                            .anyMatch(landVo -> landVo.state == EHomeType.GROWING || landVo.state == EHomeType.STEALING);
                    boolean hasBuildingWithStartTime = farmVo.buildMap.values().stream()
                            .anyMatch(buildVo -> buildVo.startTime > 0);
                    if (hasGrowingOrStealingLand || hasBuildingWithStartTime) {
                        farmVo.calculateNextCheckTime();
                        addFarmVo(farmVo.id, farmVo);
                    }else {
                        updateRedisfarmInTimer(farmVo.id);
                    }
                });
            }
        }
        long nowTime = Port.getTime();
        if(checkServerIdTT.isPeriod(nowTime)){
            checkServerId();
        }
    }

    private void checkServerId(){
        List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
        if(serverList.isEmpty()){
            Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
            return;
        }
        for(Integer serverId : serverList){
            if(!serverIdListNow.contains(serverId)){
                serverIdListNow.add(serverId);
                loadFarm(serverId);
            }
        }


    }

    private void removeFarmVo(int serverId){
        List<FarmVo> removeList = new ArrayList<>();
        for(FarmVo farmVo : farmQueueMap.values()){
            int serverIdTemp = Utils.getServerIdByHumanId(farmVo.id);
            if(serverIdTemp == serverId){
                removeList.add(farmVo);
            }
        }
        if(removeList.isEmpty()){
            return;
        }
        for(FarmVo farmVo : removeList){
            farmQueue.remove(farmVo);
            farmQueueMap.remove(farmVo.id);
        }
    }

    public void addFarmVo(Long id, FarmVo farmVo) {
        farmQueue.add(farmVo);
        farmQueueMap.put(id, farmVo);
    }

    public void removeFarmVo(Long id) {
        farmQueue.remove(farmQueueMap.get(id));
        farmQueueMap.remove(id);
        currentPulseCheckFarms.remove(id);
    }

    @DistrMethod
    public void farmPlantUpdate(Farm farm, List<Integer> landIds){
        FarmVo farmVo = new FarmVo(farm);
        updateFarmVo(farmVo, landIds);
        port.returns("farm", farm);
    }


    public void updateFarmVo(FarmVo plantFarmVo, List<Integer> landIds){
        FarmVo farmVo = farmQueueMap.getOrDefault(plantFarmVo.id,plantFarmVo);
        for (int landId : landIds) {
            LandVo landVo = plantFarmVo.landMap.get(landId);
            farmVo.landMap.put(landId, landVo);
        }
        removeFarmVo(plantFarmVo.id);
        farmVo.calculateNextCheckTime();
        farmVo.updateStateAndTime();
        if(farmVo.nextCheckTime != Long.MAX_VALUE){
            addFarmVo(farmVo.id, farmVo);
        }
        EntityManager.getEntityAsync(Human.class, farmVo.id, (res) -> {
            if (res.failed()) {
                Log.farm.error("无法获取玩家Human数据humanID={}", farmVo.id);
                return;
            }
            Human humanBasic = (Human) res.result();
            if (humanBasic == null) {
                Log.farm.error("更新农场状态失败，找不到玩家数据，id={}", farmVo.id);
                return;
            }
            humanBasic.setFarmState(farmVo.state);
            humanBasic.setFarmTime(farmVo.stateEndTime);
            humanBasic.update();
            updateRedisfarmInTimer(farmVo.id);
        });
    }

    public void updateFarmVoBuilding(FarmVo plantFarmVo, int buildId){
        FarmVo farmVo = farmQueueMap.getOrDefault(plantFarmVo.id,plantFarmVo);
        BuildVo buildVo = plantFarmVo.buildMap.get(buildId);
        farmVo.buildMap.put(buildId, buildVo);
        removeFarmVo(plantFarmVo.id);
        farmVo.calculateNextCheckTime();
        addFarmVo(farmVo.id, farmVo);
        updateRedisfarmInTimer(farmVo.id);
    }

    @DistrMethod
    public void farmFertilize(long roleId, int landId, int accTime) {
        FarmVo farmVo = farmQueueMap.get(roleId);
        ReasonResult result = new ReasonResult(true);
        result.setParam(0);
        int realAccTime = accTime;
        LandVo landVo=null;
        long pid = Port.getCurrent().createReturnAsync();
        if (farmVo == null) {
            farmLatest.incrementDataLatest(roleId);
            EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
                if (res.failed()) {
                    Log.farm.error("farmFertilize:施肥失败农场数据不存在!roleId={}", roleId);
                    port.returnsAsync(pid, "result", new ReasonResult(false), "landVo", null, "realAccTime", 0);
                    farmLatest.decrementDataLatest(farmVo.id, null);
                    return;
                }
                Farm farm = res.result();
                farm = farmLatest.getOrDefaultDataLatest(roleId, farm);
                if (farm == null) {
                    result.success = false;
                    result.setParam(117);//农场数据不存在
                    port.returnsAsync(pid, "result", result);
                    Log.farm.error("farmFertilize:施肥失败农场数据不存在!roleId={}", roleId);
                    farmLatest.decrementDataLatest(farmVo.id, null);
                    return;
                }
                LandVo tempLandVo = new LandVo();
                tempLandVo.fromJsonString(HomeManager.inst().getLand(farm, landId));
//                port.returnsAsync(pid, "result", result, "landVo", tempLandVo);
                farmLatest.decrementDataLatest(roleId, farm);
                FarmVo findFarmVo = farmQueueMap.get(roleId);
                if(findFarmVo == null){
                    findFarmVo = new FarmVo(farm);
                }else {
                    Log.farm.info("促发了可能导致00的情况");
                }
                farmLandFertilize(findFarmVo, tempLandVo, result, accTime, pid);
            });
        } else {
            landVo = farmVo.landMap.get(landId);
            farmLandFertilize(farmVo, landVo, result, accTime, pid);
        }
    }
    private void farmLandFertilize(FarmVo farmVo, LandVo landVo, ReasonResult result, int accTime, long pid){
            if(landVo == null || landVo.landId == 0){
                result.success = false;
                result.setParam(118);//该田地未解锁
                port.returnsAsync(pid,"result", result,"landVo", landVo);
                return;
            }
            if (landVo.cropId == 0 || landVo.cfgId == 0) {
                result.success = false;
                result.setParam(119);//该田地未种植
                port.returnsAsync(pid,"result", result,"landVo", landVo);
                return;
            }
            if(landVo.state != EHomeType.GROWING){
                result.success = false;
                result.setParam(120);//该田地不可施肥
                port.returnsAsync(pid,"result", result,"landVo", landVo);
                return;
            }
            // 计算实际使用的加速时间
            int finalRealAccTime = Math.min(accTime, landVo.endTime - landVo.startTime - landVo.accTime);
            checkAndUpdateLandState(landVo, accTime);
            int landId = landVo.landId;
            farmVo.landMap.put(landVo.landId, landVo);
            removeFarmVo(farmVo.id);
            farmVo.calculateNextCheckTime();
            if(farmVo.nextCheckTime != Long.MAX_VALUE){
                addFarmVo(farmVo.id, farmVo);
            }
//            port.returnsAsync(pid,"result", result,"landVo", landVo,"realAccTime", finalRealAccTime);
            EntityManager.getEntityAsync(Human.class, farmVo.id, (res1)->{
                if(res1.failed()){
                    Log.farm.error("无法获取玩家Human数据humanID={}", farmVo.id);
                    port.returnsAsync(pid,"result", result,"landVo", landVo,"realAccTime", finalRealAccTime);
                    farmLatest.decrementDataLatest(farmVo.id, null);
                    return;
                }
                Human human = res1.result();
                if(human == null){
                    Log.farm.error("无法获取玩家Human数据humanID={}", farmVo.id);
                    port.returnsAsync(pid,"result", result,"landVo", landVo,"realAccTime", finalRealAccTime);
                    farmLatest.decrementDataLatest(farmVo.id, null);
                    return;
                }
                if(farmVo.updateStateAndTime() || landVo.endTime - landVo.accTime < human.getFarmTime()){
                    human.setFarmState(farmVo.state);
                    human.setFarmTime(landVo.endTime - landVo.accTime);
                    human.update();
                }
                long roleId = farmVo.id;
                farmLatest.incrementDataLatest(roleId);
                EntityManager.getEntityAsync(Farm.class, roleId, (res2)->{
                    if(res2.failed()){
                        Log.farm.error("无法获取玩家Farm数据humanID={}", roleId);
                        port.returnsAsync(pid,"result", result,"landVo", landVo,"realAccTime", finalRealAccTime);
                        farmLatest.decrementDataLatest(roleId, null);
                        return;
                    }
                    Farm farm = res2.result();
                    farm = farmLatest.getOrDefaultDataLatest(roleId, farm);
                    if(farm == null){
                        port.returnsAsync(pid,"result", result, "landVo", null,"realAccTime", 0);
                        Log.farm.error("无法获取玩家Farm数据humanID={}", roleId);
                        farmLatest.decrementDataLatest(roleId, farm);
                        return;
                    }
                    HomeManager.inst().setLand(farm, landId, landVo.toJsonString());
                    farm.update();
                    farmLatest.decrementDataLatest(farm.getId(), farm);
                    updateRedisfarmInTimer(farmVo.id);
                    port.returnsAsync(pid,"result", result,"landVo", landVo,"realAccTime", finalRealAccTime);
                });
        });
    }



    private boolean checkAndUpdateLandState(LandVo landVo, int accTime) {
        if (landVo.state != EHomeType.GROWING) {
            return false;
        }

        int currentTime = Utils.getTimeSec();
        if (currentTime + accTime > landVo.endTime - landVo.accTime) {
            landVo.toMature();
            return true;
        }

        landVo.accTime += accTime;
        return false;
    }

    @DistrMethod
    public void farmPickOther(RobberVo robberVo, long roleId, int landId) {
        long pid = Port.getCurrent().createReturnAsync();
        farmLatest.incrementDataLatest(roleId);
        EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
            if (res.failed()) {
                Log.farm.error("farmPickOther:偷取失败农场数据不存在!roleId={}", roleId);
                port.returnsAsync(pid, "result", new ReasonResult(false), "landVo", null);
                farmLatest.decrementDataLatest(roleId, null);
                return;
            }
            FarmVo farmVo = farmQueueMap.get(roleId);
            Farm farm = (Farm) res.result();
            farm = farmLatest.getOrDefaultDataLatest(roleId,farm);
            if (farm == null) {
                Log.farm.error("farmPickOther:偷取失败农场数据不存在!roleId={}", roleId);
                port.returnsAsync(pid, "result", new ReasonResult(false), "landVo", null);
                farmLatest.decrementDataLatest(farm.getId(), farm);
                return;
            }
            if (farmVo == null) {
                LandVo landVo = new LandVo();
                landVo.fromJsonString(HomeManager.inst().getLand(farm, landId));
                ReasonResult result = HomeManager.inst().stealCheckAndSet(landVo, robberVo);
                if (result.success) {
                    farmVo = new FarmVo(farm);
                    farmVo.landMap.put(landId, landVo);
                    updateFarmVo(farmVo, Collections.singletonList(landId));
                    HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
                    farm.update();
                }
                farmLatest.decrementDataLatest(farm.getId(), farm);
                port.returnsAsync(pid, "result", result, "landVo", landVo);
                return;
            }
            LandVo landVo = farmVo.landMap.get(landId);
            ReasonResult result = HomeManager.inst().stealCheckAndSet(landVo, robberVo);
            if (result.success) {
                farmVo.landMap.put(landId, landVo);
                updateFarmVo(farmVo, Collections.singletonList(landId));
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
                farm.update();
                farmLatest.decrementDataLatest(farm.getId(), farm);
            }
            port.returnsAsync(pid, "result", result, "landVo", landVo);
        });
    }

    public void  checkLandAndBuildingState(FarmVo farmVo, Handler<AsyncResult<Boolean>> onComplete) {
        int currentTime = Utils.getTimeSec();
        if(currentPulseCheckFarms.containsKey(farmVo.id)){
            onComplete.handle(Future.succeededFuture(false));
            return;
        }
        currentPulseCheckFarms.put(farmVo.id, farmVo);
        farmLatest.incrementDataLatest(farmVo.id);
        EntityManager.getEntityAsync(Farm.class,farmVo.id, (res)->{
            if (res.failed()) {
                Log.farm.error("获取农场数据失败", farmVo.id);
                currentPulseCheckFarms.remove(farmVo.id);
                onComplete.handle(Future.succeededFuture(false));
                farmLatest.decrementDataLatest(farmVo.id,null);
                return;
            }
            Farm farm = res.result();
            farm = farmLatest.getOrDefaultDataLatest(farmVo.id,farm);

            //处理逻辑，再标记好最近成熟的那个时间
            for (LandVo landVo : farmVo.landMap.values()) {
                if ((landVo.state == EHomeType.GROWING)
                        && currentTime >= landVo.endTime-landVo.accTime) {
                    farmVo.handleLandStateGrowingEnd(landVo.landId,farm);
                }
                if ((landVo.state == EHomeType.STEALING)
                        && currentTime >= landVo.endTime) {
                    farmVo.handleLandStateStealingEnd(landVo.landId,farm);
                }
            }
            for (BuildVo buildVo : farmVo.buildMap.values()) {
                if (buildVo.startTime > 0 && currentTime >= buildVo.endTime-buildVo.accTime) {
                    farmVo.handleBuildingUpgradeEnd(buildVo, farm);
                }
            }
            farmLatest.decrementDataLatest(farmVo.id,farm);
            currentPulseCheckFarms.remove(farmVo.id);
            onComplete.handle(Future.succeededFuture(true));
        });
    }


    @DistrMethod
    public void buildingSpeedUp(long humanId, int buildId, int accTime) {
        long pid = Port.getCurrent().createReturnAsync();
        farmLatest.incrementDataLatest(humanId);
        EntityManager.getEntityAsync(Farm.class, humanId, (res) -> {
            if (res.failed()) {
                Log.farm.error("建筑加速农场数据获取失败!humanId={}", humanId);
                return;
            }
            FarmVo farmVo = farmQueueMap.get(humanId);
            Farm farm = (Farm) res.result();
            farm = farmLatest.getOrDefaultDataLatest(humanId, farm);
            if (farm == null) {
                Log.farm.error("建筑加速农场数据不存在!humanId={}", humanId);
                farmLatest.decrementDataLatest(humanId, null);
                return;
            }
            if (farmVo == null) {
                farmVo = new FarmVo(farm);
                BuildVo buildVo = farmVo.buildMap.get(buildId);
                buildVo.accTime += accTime;
                farmVo.buildMap.put(buildId, buildVo);
                removeFarmVo(farmVo.id);
                farmVo.calculateNextCheckTime();
                if (farmVo.nextCheckTime != Long.MAX_VALUE) {
                    addFarmVo(farmVo.id, farmVo);
                }
                updateRedisfarmInTimer(farm.getId());
                farm.setBuildMap(BuildVo.mapToJsonString(farmVo.buildMap));
                farm.update();
                farmLatest.decrementDataLatest(farm.getId(), farm);
                Log.farm.error("建筑加速农场数据不存在定时队列中!humanId={}", humanId);
                return;
            }
            BuildVo buildVo = farmVo.buildMap.get(buildId);
            buildVo.accTime += accTime;
            farmVo.buildMap.put(buildId, buildVo);
            removeFarmVo(farmVo.id);
            farmVo.calculateNextCheckTime();
            if (farmVo.nextCheckTime != Long.MAX_VALUE) {
                addFarmVo(farmVo.id, farmVo);
            }
            farm.setBuildMap(BuildVo.mapToJsonString(farmVo.buildMap));
            farm.update();
            farmLatest.decrementDataLatest(farm.getId(), farm);
            updateRedisfarmInTimer(farmVo.id);
        });
    }

    @DistrMethod
    public void pickSelf(long roleId, int landId) {
        long pid = Port.getCurrent().createReturnAsync();
        farmLatest.incrementDataLatest(roleId);
        EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
            if (res.failed()) {
                Log.farm.error("pickSelf:收获失败农场数据不存在!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId, null);
                return;
            }
            Farm farm = (Farm) res.result();
            farm = farmLatest.getOrDefaultDataLatest(roleId, farm);
            if (farm == null) {
                Log.farm.error("pickSelf:收获失败农场数据不存在!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId, null);
                return;
            }
            FarmVo farmVo = farmQueueMap.get(roleId);
            if (farmVo == null) {
                _result_dataPickSelf(farm, landId, pid);
                farmLatest.decrementDataLatest(farm.getId(), farm);
                return;
            }

            ReasonResult result = HomeManager.inst().pickCheckAndSet(farmVo, landId);
            if (result.success) {
                updateFarmVo(farmVo, Collections.singletonList(landId));
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
            }else {
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
            }
            farm.update();
            farmLatest.decrementDataLatest(farm.getId(), farm);
            port.returnsAsync(pid,"result", result, "landVo", farmVo.landMap.get(landId));
        });
    }

    private void _result_dataPickSelf(Farm farm, int landId, long pid) {
        FarmVo farmVo = new FarmVo(farm);
        LandVo oldLandVo = farmVo.landMap.get(landId);
        if(oldLandVo == null){
            Log.farm.error("pickSelf:收获失败农场数据不存在!roleId={}", farm.getId());
            return;
        }
        ReasonResult result = HomeManager.inst().pickCheckAndSet(farmVo, landId);
        if (result.success) {
            updateFarmVo(farmVo, Collections.singletonList(landId));
            HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
        }
        farm.update();
        port.returnsAsync(pid,"result", result, "landVo", oldLandVo, "farm", farm);

    }

    /**
     * 驱赶小偷
     */
    @DistrMethod
    public void farmBattleWin(long farmId, int landId) {
        long pid = Port.getCurrent().createReturnAsync();
        farmLatest.incrementDataLatest(farmId);
        EntityManager.getEntityAsync(Farm.class, farmId, (res) -> {
            if (res.failed()) {
                Log.farm.error("farmBattleWin:驱赶失败农场数据不存在!roleId={}", farmId);
                port.returns("result", new ReasonResult(false));
                farmLatest.decrementDataLatest(farmId, null);
                return;
            }
            FarmVo farmVo = farmQueueMap.get(farmId);
            Farm farm = (Farm) res.result();
            farm = farmLatest.getOrDefaultDataLatest(farmId,farm);
            if (farm == null) {
                Log.farm.error("farmBattleWin:驱赶失败农场数据不存在!roleId={}", farmId);
                port.returnsAsync(pid,"result", new ReasonResult(false));
                farmLatest.decrementDataLatest(farm.getId(), farm);
                return;
            }
            if (farmVo == null) {
                LandVo landVo = new LandVo();
                landVo.fromJsonString(HomeManager.inst().getLand(farm, landId));
                long robId = landVo.robber.id;
                ReasonResult result = HomeManager.inst().battleCheckAndSet(landVo);
                if (result.success) {
                    farmVo = new FarmVo(farm);
                    farmVo.landMap.put(landId, landVo);
                    updateFarmVo(farmVo, Collections.singletonList(landId));
                    HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
                    farm.update();
                    HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                    // 被偷失败
                    // prx.getInfo(farmId);
                    // prx.listenResult(this::_result_globalStolenFailed, "landVo", landVo);
                    // 偷失败
                    prx.getInfo(robId);
                    prx.listenResult(this::_result_globalStealFailed, "landVo", landVo);
                }
                port.returnsAsync(pid,"result", result);
                farmLatest.decrementDataLatest(farm.getId(), farm);
                return;
            }
            LandVo landVo = farmVo.landMap.get(landId);
            long robId = landVo.robber.id;
            ReasonResult result = HomeManager.inst().battleCheckAndSet(landVo);
            if (result.success) {
                farmVo.landMap.put(landId, landVo);
                updateFarmVo(farmVo, Collections.singletonList(landId));
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
                farm.update();
                farmLatest.decrementDataLatest(farm.getId(), farm);
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                // 被偷失败
                // prx.getInfo(farmId);
                // prx.listenResult(this::_result_globalStolenFailed, "landVo", landVo);
                // 偷失败
                prx.getInfo(robId);
                prx.listenResult(this::_result_globalStealFailed, "farmId", farmId, "landVo", landVo);
            }
            port.returnsAsync(pid,"result", result);
        });
    }

    private void _result_globalStealFailed(Param results, Param context) {
        long farmId = context.get("farmId");
        LandVo landVo = context.get("landVo");
        HumanGlobalInfo info = results.get();
        if (info != null) {
            // 在线
            HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.stealFaild(farmId, landVo);
        }else {
            JSONObject jo = new JSONObject();
            jo.put("t", 0);//偷失败
            jo.put("fId", farmId);
            jo.put("landVo", landVo.toJsonString());
            Pocket.add(landVo.robber.id, PocketLineEventSubKey.FARM_STEAL, jo.toJSONString());
        }
    }


    @DistrMethod
    public void farmHarvest(long roleId, int landId) {
        long pid = Port.getCurrent().createReturnAsync();
        farmLatest.incrementDataLatest(roleId);
        EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
            if (res.failed()) {
                Log.farm.error("farmHarvest:无法获取农场数据!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId, null);
                return;
            }
            Farm farm = (Farm) res.result();
            if (farm == null) {
                Log.farm.error("farmHarvest:收获失败农场数据不存在!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId, null);
                return;
            }
            farm = farmLatest.getOrDefaultDataLatest(roleId, farm);
            FarmVo farmVo = farmQueueMap.get(roleId);
            if (farmVo == null) {
                farmVo = new FarmVo(farm);
            }
            LandVo oldLandVo = farmVo.landMap.get(landId);
            List<Integer> upgradeConfList = new ArrayList<>();
            ReasonResult result = HomeManager.inst().harvestCheckAndSet(farmVo, landId);
            if (result.success) {
                updateFarmVo(farmVo, Collections.singletonList(landId));
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
                // 处理庄园升级，原来在回调处理，导致客户端一键收获，该方法调用多次，但经验只加一次
                ConfFarmGreens confFarmGreens = ConfFarmGreens.get(oldLandVo.cfgId);
                if (confFarmGreens == null) {
                    Log.farm.error("confFarmGreens is null cfgId={}", oldLandVo.cfgId);
                } else {
                    farm.setExp(farm.getExp() + confFarmGreens.value);
                    ConfFarmLevel confNextLevel = ConfFarmLevel.get(farm.getLevel() + 1);
                    while (confNextLevel != null && farm.getExp() >= confNextLevel.expend) {
                        farm.setLevel(confNextLevel.sn);// 升级
                        upgradeConfList.add(confNextLevel.sn);// 把升级过的等级记录
                        confNextLevel = ConfFarmLevel.get(farm.getLevel() + 1);// 下一等级
                    }
                }
            }else {
                HomeManager.inst().setLand(farm, landId, farmVo.landMap.get(landId).toJsonString());
            }
            farmLatest.decrementDataLatest(farm.getId(), farm);
            if(!farmLatest.isExist(roleId)){
                farm.update();
            }
            port.returnsAsync(pid,"result", result, "oldLandVo", oldLandVo, "farm", farm, "upgradeConfList", upgradeConfList);
        });
    }

    @DistrMethod
    public void farmBuildUpdate(long roleId, BuildVo buildVo) {
        farmLatest.incrementDataLatest(roleId);
        EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
            if (res.failed()) {
                Log.farm.error("farmBuildUpdate:无法获取农场数据!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId,null);
                return;
            }
            Farm farm = res.result();
            if (farm == null) {
                Log.farm.error("farmBuildUpdate:建筑更新失败农场数据不存在!roleId={}", roleId);
                farmLatest.decrementDataLatest(roleId,null);
                return;
            }
            farm = farmLatest.getOrDefaultDataLatest(roleId, farm);
            FarmVo farmVo = farmQueueMap.get(roleId);
            if (farmVo == null) {
                farmVo = new FarmVo(farm);
            }
            farmVo.buildMap.put(buildVo.cfgId, buildVo);
            updateFarmVoBuilding(farmVo, buildVo.cfgId);
            HomeManager.inst().setBuilding(farm, buildVo);
            farm.update();
            farmLatest.decrementDataLatest(roleId,farm);
        });
    }

    private void updateRedisfarmInTimer(long roleId){
        if(farmQueueMap.containsKey(roleId)){
            RedisTools.addToSet(EntityManager.getRedisClient(),Utils.getRedisKey(roleId, RedisKeys.farmInTimer),Long.toString(roleId));
        }else {
            RedisTools.delToSet(EntityManager.getRedisClient(),Utils.getRedisKey(roleId, RedisKeys.farmInTimer), Long.toString(roleId));
        }
    }

}
