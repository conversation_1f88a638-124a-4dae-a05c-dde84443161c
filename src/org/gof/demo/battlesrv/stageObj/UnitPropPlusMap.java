package org.gof.demo.battlesrv.stageObj;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.battlesrv.support.PropCalcCommon;
import org.gof.demo.worldsrv.config.ConfAttrSource;
import org.gof.demo.worldsrv.entity.EntityUnitPropPlus;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

public class UnitPropPlusMap implements ISerilizable{
	public Map<String, Map<String, BigDecimal>> dataMap = new HashMap<>();
	public UnitPropPlus unitPropPlus = new UnitPropPlus();
	private PropCalcCommon propCalc = null;

	private static final String[] FIELD_NAMES = {
			"",//0
			UnitPropPlus.K.initAttr,//1
			UnitPropPlus.K.equip,//2
			UnitPropPlus.K.jobAwake,//3
			UnitPropPlus.K.pet,//4
			UnitPropPlus.K.skill,//5
			UnitPropPlus.K.science,//6
			UnitPropPlus.K.relic,//7
			"atlas",//8
			UnitPropPlus.K.mount,//9
			UnitPropPlus.K.build,//10
			UnitPropPlus.K.statue,//11
			"nothing",			 //12
			UnitPropPlus.K.artifact,//13
			UnitPropPlus.K.fate,//14
			"markAwake",//15
			UnitPropPlus.K.skin,//16
			UnitPropPlus.K.adventureTask,//17
			UnitPropPlus.K.carParkSkin,//18
			UnitPropPlus.K.privilege,//19
			"artifactRefine",//20
			UnitPropPlus.K.wing,//21
			"lock",//22
			"",//23
			UnitPropPlus.K.flyPet,
			UnitPropPlus.K.angel,
			UnitPropPlus.K.charm,
	};
	
	/**
	 * 初始化 在读取数据库以后调用
	 */
	public void init(UnitPropPlus unitPropPlus) {
		if(unitPropPlus==null){
			return;
		}
		this.unitPropPlus = unitPropPlus;
		for(EntityUnitPropPlus k : EntityUnitPropPlus.values()) {
			String pp = this.fieldRead(unitPropPlus, k.name());
			dataMap.put(k.name(), toMap(pp));
		}
	}
	
	public String fieldRead(UnitPropPlus unitPropPlus, String name){
		switch(name){
			case UnitPropPlus.K.base:
				return unitPropPlus.getBase();
			case UnitPropPlus.K.level:
				return unitPropPlus.getLevel();
			case UnitPropPlus.K.initAttr:
				return unitPropPlus.getInitAttr();
			case UnitPropPlus.K.buff:
				return unitPropPlus.getBuff();
			case UnitPropPlus.K.pet:
				return unitPropPlus.getPet();
			case UnitPropPlus.K.propConvert:
				return unitPropPlus.getPropConvert();
			case UnitPropPlus.K.skill:
				return unitPropPlus.getSkill();
			case UnitPropPlus.K.equip:
				return unitPropPlus.getEquip();
			case UnitPropPlus.K.relic:
				return unitPropPlus.getRelic();
			case UnitPropPlus.K.fate:
				return unitPropPlus.getFate();
			case UnitPropPlus.K.artifact:
				return unitPropPlus.getArtifact();
			case UnitPropPlus.K.mount:
				return unitPropPlus.getMount();
			case UnitPropPlus.K.wing:
				return unitPropPlus.getWing();
			case UnitPropPlus.K.statue:
				return unitPropPlus.getStatue();
			case UnitPropPlus.K.science:
				return unitPropPlus.getScience();
			case UnitPropPlus.K.skin:
				return unitPropPlus.getSkin();
			case UnitPropPlus.K.adventureTask:
				return unitPropPlus.getAdventureTask();
			case UnitPropPlus.K.privilege:
				return unitPropPlus.getPrivilege();
			case UnitPropPlus.K.carPark:
				return unitPropPlus.getCarPark();
			case UnitPropPlus.K.carParkSkin:
				return unitPropPlus.getCarParkSkin();
			case UnitPropPlus.K.gm:
				return unitPropPlus.getGm();
			case UnitPropPlus.K.jobAwake:
				return unitPropPlus.getJobAwake();
			case UnitPropPlus.K.build:
				return unitPropPlus.getBuild();
			case UnitPropPlus.K.illustrated:
				return unitPropPlus.getIllustrated();
			case UnitPropPlus.K.flyPet:
				return unitPropPlus.getFlyPet();
			case UnitPropPlus.K.angel:
				return unitPropPlus.getAngel();
			case UnitPropPlus.K.wingTalent:
				return unitPropPlus.getWingTalent();
			case UnitPropPlus.K.charm:
				return unitPropPlus.getCharm();
			case UnitPropPlus.K.fish:
				return unitPropPlus.getFish();
			case UnitPropPlus.K.fishShow:
				return unitPropPlus.getFishShow();
			default:
				Log.temp.error("===新加字段未实现代码，name={}", name);
				return null;
		}
	}

	public void setFieldRead(String name, String json){
		switch(name){
			case UnitPropPlus.K.base:
				this.unitPropPlus.setBase(json);
				break;
			case UnitPropPlus.K.level:
				this.unitPropPlus.setLevel(json);
				break;
			case UnitPropPlus.K.initAttr:
				this.unitPropPlus.setInitAttr(json);
				break;
			case UnitPropPlus.K.buff:
				this.unitPropPlus.setBuff(json);
				break;
			case UnitPropPlus.K.pet:
				this.unitPropPlus.setPet(json);
				break;
			case UnitPropPlus.K.skill:
				this.unitPropPlus.setSkill(json);
				break;
			case UnitPropPlus.K.equip:
				this.unitPropPlus.setEquip(json);
				break;
			case UnitPropPlus.K.relic:
				this.unitPropPlus.setRelic(json);
				break;
			case UnitPropPlus.K.fate:
				this.unitPropPlus.setFate(json);
				break;
			case UnitPropPlus.K.artifact:
				this.unitPropPlus.setArtifact(json);
				break;
			case UnitPropPlus.K.mount:
				this.unitPropPlus.setMount(json);
				break;
			case UnitPropPlus.K.wing:
				this.unitPropPlus.setWing(json);
				break;
			case UnitPropPlus.K.statue:
				this.unitPropPlus.setStatue(json);
				break;
			case UnitPropPlus.K.science:
				this.unitPropPlus.setScience(json);
				break;
			case UnitPropPlus.K.skin:
				this.unitPropPlus.setSkin(json);
				break;
			case UnitPropPlus.K.adventureTask:
				this.unitPropPlus.setAdventureTask(json);
				break;
			case UnitPropPlus.K.privilege:
				this.unitPropPlus.setPrivilege(json);
				break;
			case UnitPropPlus.K.carPark:
				this.unitPropPlus.setCarPark(json);
				break;
			case UnitPropPlus.K.carParkSkin:
				this.unitPropPlus.setCarParkSkin(json);
				break;
			case UnitPropPlus.K.gm:
				this.unitPropPlus.setGm(json);
				break;
			case UnitPropPlus.K.jobAwake:
				this.unitPropPlus.setJobAwake(json);
				break;
			case UnitPropPlus.K.build:
				this.unitPropPlus.setBuild(json);
				break;
			case UnitPropPlus.K.illustrated:
				this.unitPropPlus.setIllustrated(json);
				break;
			case UnitPropPlus.K.flyPet:
				this.unitPropPlus.setFlyPet(json);
				break;
			case UnitPropPlus.K.angel:
				this.unitPropPlus.setAngel(json);
				break;
			case UnitPropPlus.K.wingTalent:
				this.unitPropPlus.setWingTalent(json);
				break;
			case UnitPropPlus.K.charm:
				this.unitPropPlus.setCharm(json);
				break;
			case UnitPropPlus.K.fish:
				this.unitPropPlus.setFish(json);
				break;
			case UnitPropPlus.K.fishShow:
				this.unitPropPlus.setFishShow(json);
				break;
			default:
				Log.temp.error("===新加字段未实现代码，name={}", name);
				break;
		}
		dataMap.put(name, toMap(json));
		unitPropPlus.update();
	}

	public Map<String, BigDecimal> toMap(String pp) {
		propCalc = new PropCalcCommon();
		propCalc.removeAll();
		propCalc.plus(pp);
		return propCalc.getDatas();
	}


	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(dataMap);
		out.write(unitPropPlus);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		dataMap = in.read();
		unitPropPlus = in.read();
	}

	public List<Define.p_system_sp> to_p_system_spList() {
		Collection<ConfAttrSource> confs = ConfAttrSource.findAll();
		List<Define.p_system_sp> list = new ArrayList<>();
		for (ConfAttrSource conf : confs) {
			if (conf.sn >= FIELD_NAMES.length || conf.sn <= 0) {
				continue;
			}
			String key = FIELD_NAMES[conf.sn];
			if (!dataMap.containsKey(key)) {
				continue;
			}
			Define.p_system_sp.Builder sp = Define.p_system_sp.newBuilder();
			sp.setType(conf.sn);
			Map<String, BigDecimal> map = dataMap.get(FIELD_NAMES[conf.sn]);

			//合并坐骑改装属性到停车场皮肤属性
			if (key.equals(UnitPropPlus.K.carParkSkin)) {
				if(map != null) {
					map = new HashMap<>(map);
				}
				Map<String, BigDecimal> mapMount = dataMap.get(UnitPropPlus.K.carPark);
				if (mapMount != null) {
					if (map == null) {
						map = mapMount;
					}else {
						for (Map.Entry<String, BigDecimal> entry : mapMount.entrySet()) {
							map.merge(entry.getKey(), entry.getValue(), BigDecimal::add);
						}
					}
				}
			}
			//合并翅膀天赋属性到翅膀属性
			if (key.equals(UnitPropPlus.K.wing)) {
				if(map != null) {
					map = new HashMap<>(map);
				}
				Map<String, BigDecimal> mapWingTalent = dataMap.get(UnitPropPlus.K.wingTalent);
				if (mapWingTalent != null) {
					if (map == null) {
						map = mapWingTalent;
					}else {
						for (Map.Entry<String, BigDecimal> entry : mapWingTalent.entrySet()) {
							map.merge(entry.getKey(), entry.getValue(), BigDecimal::add);
						}
					}
				}
			}
			//合并图鉴属性到伙伴属性
			if (key.equals(UnitPropPlus.K.pet)) {
				if(map != null) {
					map = new HashMap<>(map);
				}
				Map<String, BigDecimal> mapIllustrated = dataMap.get(UnitPropPlus.K.illustrated);
				if (mapIllustrated != null) {
					if (map == null) {
						map = mapIllustrated;
					}else {
						for (Map.Entry<String, BigDecimal> entry : mapIllustrated.entrySet()) {
							map.merge(entry.getKey(), entry.getValue(), BigDecimal::add);
						}
					}
				}
			}
			if (map == null) {
				continue;
			}
			for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
				sp.addAttrList(Define.p_key_value.newBuilder().setK(Integer.valueOf(entry.getKey())).setV(entry.getValue().longValue()));
			}
			list.add(sp.build());
		}
		return list;
	}
}
