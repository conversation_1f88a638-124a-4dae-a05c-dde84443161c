package org.gof.demo.battlesrv.manager;

import com.pwrd.op.LogOp;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.S;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropCalcCommon;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

/**
 * 属性计算管理器
 */
public class PropManager extends ManagerBase {

    public static PropManager inst() {
        return inst(PropManager.class);
    }

    public double PERCENTDIV = 10000D;

    /**
     * 计算当前战斗单位属性
     *
     * @param unitObj
     * @param maxType 0 表示增加上限和血量的deta,减只减少血量到最大(用于装备和buff,变身)
     *                1 表示变更上限,(血量超了则到最大, 用于特定增加血量上限的技能)
     *                2 表示增加上限,当前血量按deta计算,减少也用deta计算(用于buff)
     *                3 表示增加上限,当前血量按百分比计算,减少也用百分比计算(用于buff)
     */

    public void propCalc(UnitObject unitObj, CombatChangeLog log) {
        if (unitObj.isHumanObj()){
            HumanObject humanObj = unitObj.getHumanObj();
            if(humanObj.isMirror){
                return;
            }
            // 如果不是跨服，且玩家在跨服则不处理
            if(!S.isBridge && humanObj.bridge){
                return;
            }
            Human human = humanObj.getHuman();
            Human2 human2 = humanObj.getHuman2();
            Human3 human3 = humanObj.getHuman3();
            BigDecimal topCombat = new BigDecimal(human3.getTopCombat());
            BigDecimal combatNew = getCombat(humanObj).setScale(0, RoundingMode.DOWN);
            int topResult = topCombat.compareTo(combatNew);
            if(topResult < 0){// -1 topCombat 小于 combatNew ， 0 相等， 1 topCombat 大于 combatNew
                human3.setTopCombat(combatNew.toString());
                // 添加战力榜
                RankManager.inst().rankUpdate(RedisKeys.humanCombatList + human.getServerId(), human3.getTopCombat(), String.valueOf(humanObj.id));
            }
            BigDecimal combat = new BigDecimal(human.getCombat());
            int value = combat.compareTo(combatNew);
            if(value != 0){
                humanObj.isCombatUp = true;
                human.setCombat(combatNew.toString());
                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_45);
                ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_45, 0);
                HumanManager.inst().sendMsg_role_power_info_s2c(humanObj);
                HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
                if(human2.getGuildId() > 0){
                    Param param = new Param();
                    param.put("name", human.getName());
                    param.put("level", human.getLevel());
                    param.put("combat", combatNew.longValue());
                    GuildServiceProxy proxy = GuildServiceProxy.newInstance();
                    proxy.updateMemberCombat(human2.getGuildId(), humanObj.id, param);
                }
                LogOp.log("combatChange",humanObj.id, Port.getTime(),Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                        humanObj.name, humanObj.getHuman().getLevel(), humanObj.getHuman().getServerId(), combat, value, "");
            }
        }
    }

    public BigDecimal getCombat(UnitObject unitObj){
        BigDecimal combat = BigDecimal.ZERO;
        List<EntityUnitPropPlus> excludeNameList = new ArrayList<>();
        excludeNameList.add(EntityUnitPropPlus.equip);
        // 各模块加成属性
        PropCalc propPlus = HumanManager.inst().getPropPlus(unitObj.dataPers.unitPropPlus, excludeNameList);
        long combatLong = 0;
        for (Map.Entry<Integer, BigDecimal> entry : propPlus.getDatas().entrySet()) {
            BigDecimal value = entry.getValue();
            if(value.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            float mul =GlobalConfVal.getCombatCoefficient(entry.getKey());
            if(mul == 0){
                continue;
            }
            BigDecimal valueNew = value.multiply(BigDecimal.valueOf(mul));
            combatLong += value.multiply(BigDecimal.valueOf(mul)).longValue();
            combat = combat.add(valueNew);
        }

//        Log.temp.info("===玩家战力combat= {}", combat);

        if(unitObj instanceof HumanObject){
            HumanObject humanObj = (HumanObject)unitObj;
            int tab = humanObj.getHuman2().getEquipTab();
            Equip equip = humanObj.operation.equipsMap.get(tab);
            if(equip != null){
                // 装备公式2.0、装备战力=（生命战力+攻击战力+防御战力+攻速战力+词条1战力+词条2战力）*（1+等级系数）
                BigDecimal equipCombat = BigDecimal.ZERO;
                for (int i = weapon; i <= EquipInfo.max_part; ++i){
                    String jsonString = EquipManager.inst().getEquipPart(equip,i);
                    if(!Utils.isNullOrEmptyJSONString(jsonString)){
                        EquipInfo equipInfo = new EquipInfo();
                        equipInfo.fromJsonString(jsonString);
                        PropCalc attrAll = new PropCalc();
                        attrAll.plus(equipInfo.baseAttr);
                        attrAll.plus(equipInfo.randAttr);
                        equipCombat = equipCombat.add(getEquipCombat(attrAll, equipInfo.lv));
                    }
                }
//                Log.temp.info("===装备战力equipCombat= {}", equipCombat);
                combat = combat.add(equipCombat);
            }

            String powerJSON = humanObj.getHuman3().getModPowerJSON();
            Map<Integer, String> modPowerMap = Utils.jsonToMapIntString(powerJSON);
            long value = 0;
            for(String valueStr : modPowerMap.values()){
                value += Utils.longValue(valueStr);
            }
//            Log.temp.info("===功能模块,战力= {}", value);
            combat = combat.add(BigDecimal.valueOf(value));
        }

//        Log.temp.info("====最终战力combat={}, " , combat);
        return combat;
    }

    /**
     *  装备公式2.0、装备战力=（生命战力+攻击战力+防御战力+攻速战力+词条1战力+词条2战力）*（1+等级系数）。等级系数=0.05+（等级-10/1.1）*0.01（向上取整）
     * @param propPlus
     * @param quality
     * @return
     */
    public BigDecimal getEquipCombat(PropCalc propPlus, int level){
        BigDecimal equipCombat = BigDecimal.ZERO;
        for (Map.Entry<Integer, BigDecimal> entry : propPlus.getDatas().entrySet()) {
            BigDecimal value = entry.getValue();
            if (value.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            int propId = entry.getKey();
            float mul = GlobalConfVal.getCombatCoefficient(propId);
            if (mul == 0) {
                continue;
            }
            // 1.0-1.5公式 某属性战斗力 = 数值 * 属性系数 * （1+品质系数*（品质等阶-2））
            // 装备公式2.0、装备战力=（生命战力+攻击战力+防御战力+攻速战力+词条1战力+词条2战力）*（1+等级系数）
            if(propId == 1003){ // 攻速战力 = n * 25 + （n-1)*15/2。  n=(攻速（攻速*系数）-0.09)*100
                double n = (value.longValue() * mul / 10000 - 0.09) * 100;
                equipCombat = equipCombat.add(BigDecimal.valueOf(n * 25 + (n - 1) * 15 / 2));
            } else {
                BigDecimal valueNew = value.multiply(BigDecimal.valueOf(mul));
                equipCombat = equipCombat.add(valueNew);
            }
        }
        if(level == 1){// 系数0
            return equipCombat;
        } else if( level < 10){// 系数0.05
            equipCombat = equipCombat.multiply(BigDecimal.valueOf(1 + 0.05));
        } else {
            double lvX = 0.05+ (int) Math.ceil((level - 10 / 1.1)) * 0.01;
            equipCombat = equipCombat.multiply(BigDecimal.valueOf(1 + lvX));
        }
        return BigDecimal.valueOf(equipCombat.longValue());
    }

    public PropCalc reverseCalculateBaseProps(Define.p_battle_role battleRole, PropCalc propBonus) {
        PropCalc basePropCalc = new PropCalc();
        Map<Integer, BigDecimal> baseDataMap = basePropCalc.getDatas();
        Map<Integer, BigDecimal> bonusDataMap = propBonus.getDatas();

        // 从p_battle_role中获取最终属性值
        Map<Integer, Long> finalProps = new HashMap<>();
        for (Define.p_key_value attr : battleRole.getAttrListList()) {
            finalProps.put((int)attr.getK(), attr.getV());
        }

        // 逆推基础属性值
        // 攻击：1 = 1001 * (3001/10000 + 1)，所以 1001 = 1 / (3001/10000 + 1)
        BigDecimal attr_1 = BigDecimal.valueOf(finalProps.getOrDefault(1, 0L));
        BigDecimal attr_3001 = bonusDataMap.getOrDefault(3001, BigDecimal.ZERO);
        BigDecimal base_1001 = reverseCalculate(attr_1, attr_3001);
        baseDataMap.put(1001, base_1001);

        // 生命：2 = 1002 * (3002/10000 + 1)，所以 1002 = 2 / (3002/10000 + 1)
        BigDecimal attr_2 = BigDecimal.valueOf(finalProps.getOrDefault(2, 0L));
        BigDecimal attr_3002 = bonusDataMap.getOrDefault(3002, BigDecimal.ZERO);
        BigDecimal base_1002 = reverseCalculate(attr_2, attr_3002);
        baseDataMap.put(1002, base_1002);

        // 攻速：3 = 1003 * (3003/10000 + 1)，所以 1003 = 3 / (3003/10000 + 1)
        BigDecimal attr_3 = BigDecimal.valueOf(finalProps.getOrDefault(3, 0L));
        BigDecimal attr_3003 = bonusDataMap.getOrDefault(3003, BigDecimal.ZERO);
        BigDecimal base_1003 = reverseCalculate(attr_3, attr_3003);
        baseDataMap.put(1003, base_1003);

        // 防御：24 = 1024 * (3024/10000 + 1)，所以 1024 = 24 / (3024/10000 + 1)
        BigDecimal attr_24 = BigDecimal.valueOf(finalProps.getOrDefault(24, 0L));
        BigDecimal attr_3024 = bonusDataMap.getOrDefault(3024, BigDecimal.ZERO);
        BigDecimal base_1024 = reverseCalculate(attr_24, attr_3024);
        baseDataMap.put(1024, base_1024);

        return basePropCalc;
    }

    private BigDecimal reverseCalculate(BigDecimal finalValue, BigDecimal bonus) {
        if (finalValue == null || finalValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal multiplier = bonus.divide(BigDecimal.valueOf(10000), 10, RoundingMode.HALF_UP).add(BigDecimal.ONE);
        if (multiplier.compareTo(BigDecimal.ZERO) == 0) {
            return finalValue;
        }

        return finalValue.divide(multiplier, 10, RoundingMode.HALF_UP);
    }

    public Define.p_battle_role.Builder recalculateAndUpdateBattleRole(Define.p_battle_role battleRole, PropCalc propBonus) {
        // 1. 保存原始battleRole中的所有属性
        Map<Integer, Long> originalProps = new HashMap<>();
        for (Define.p_key_value attr : battleRole.getAttrListList()) {
            originalProps.put((int)attr.getK(), attr.getV());
        }

        // 2. 从battleRole逆推基础属性
        PropCalc basePropCalc = reverseCalculateBaseProps(battleRole, propBonus);

        // 3. 应用新的加成系数
        PropCalc newPropCalc = basePropCalc.mul(propBonus);

        // 4. 重新计算最终属性
        newPropCalc = recalculatePropPlus(newPropCalc);

        // 5. 将最终属性值(1,2,3,24)复制到原始属性键(1001,1002,1003,1024)
        Map<Integer, BigDecimal> dataMap = newPropCalc.getDatas();

        // 攻击：将1的值复制到1001
        if (dataMap.containsKey(1)) {
            dataMap.put(1001, dataMap.get(1));
        }

        // 生命：将2的值复制到1002
        if (dataMap.containsKey(2)) {
            dataMap.put(1002, dataMap.get(2));
        }

        // 攻速：将3的值复制到1003
        if (dataMap.containsKey(3)) {
            dataMap.put(1003, dataMap.get(3));
        }

        // 防御：将24的值复制到1024
        if (dataMap.containsKey(24)) {
            dataMap.put(1024, dataMap.get(24));
        }

        // 6. 更新battleRole中的属性值，保留所有原始属性
        Define.p_battle_role.Builder builder = battleRole.toBuilder();
        builder.clearAttrList();

        // 先添加所有重新计算的属性
        for (Map.Entry<Integer, BigDecimal> entry : dataMap.entrySet()) {
            int key = entry.getKey();
            long value = entry.getValue().longValue();

            Define.p_key_value.Builder attrBuilder = Define.p_key_value.newBuilder()
                    .setK(key)
                    .setV(value);

            builder.addAttrList(attrBuilder.build());
        }

        // 再添加原始属性中存在但新计算中不存在的属性
        for (Map.Entry<Integer, Long> entry : originalProps.entrySet()) {
            int key = entry.getKey();
            if (!dataMap.containsKey(key)) {
                Define.p_key_value.Builder attrBuilder = Define.p_key_value.newBuilder()
                        .setK(key)
                        .setV(entry.getValue());

                builder.addAttrList(attrBuilder.build());
            }
        }

        return builder;
    }

    public PropCalc recalculatePropPlus(PropCalc propCalc) {
        Map<Integer, BigDecimal> dataMap = propCalc.getDatas();
        // 最终攻击1 = （1001（基础攻击）*（3001（攻击加成）/10000+1）
        BigDecimal total_att = Utils.getBigDecimal(dataMap.get(1001),dataMap.get(3001));
        dataMap.put(1, total_att);

        //（1002（基础生命）*（3002（生命加成）/10000+1）
        BigDecimal total_hp =  Utils.getBigDecimal(dataMap.get(1002), dataMap.get(3002));
        dataMap.put(2, total_hp);

        //（1003（基础攻速）*（3003（攻速加成）/10000+1）
        BigDecimal total_att_speed = Utils.getBigDecimal(dataMap.get(1003), dataMap.get(3003));
        dataMap.put(3, total_att_speed);

        //（1024（基础防御）*（3024（防御加成）/10000+1）
        BigDecimal total_def =  Utils.getBigDecimal(dataMap.get(1024), dataMap.get(3024));
        dataMap.put(24, total_def);
        return propCalc;
    }


}
