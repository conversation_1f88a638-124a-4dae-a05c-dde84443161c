// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.charm.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgCharm {
  private MsgCharm() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface charm_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 美观值
     * </pre>
     *
     * <code>uint32 charm_value = 1;</code>
     * @return The charmValue.
     */
    int getCharmValue();

    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @return A list containing the showMedalList.
     */
    java.util.List<java.lang.Integer> getShowMedalListList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @return The count of showMedalList.
     */
    int getShowMedalListCount();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    int getShowMedalList(int index);

    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @return A list containing the receivedLevelRewardList.
     */
    java.util.List<java.lang.Integer> getReceivedLevelRewardListList();
    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @return The count of receivedLevelRewardList.
     */
    int getReceivedLevelRewardListCount();
    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @param index The index of the element to return.
     * @return The receivedLevelRewardList at the given index.
     */
    int getReceivedLevelRewardList(int index);

    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @return A list containing the unlockMedalList.
     */
    java.util.List<java.lang.Integer> getUnlockMedalListList();
    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @return The count of unlockMedalList.
     */
    int getUnlockMedalListCount();
    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @param index The index of the element to return.
     * @return The unlockMedalList at the given index.
     */
    int getUnlockMedalList(int index);

    /**
     * <pre>
     * 收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 5;</code>
     * @return The likeTotalNum.
     */
    int getLikeTotalNum();

    /**
     * <pre>
     * 今日我已领取的收藏室点赞奖励次数
     * </pre>
     *
     * <code>uint32 claimed_like_reward_num = 6;</code>
     * @return The claimedLikeRewardNum.
     */
    int getClaimedLikeRewardNum();

    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @return A list containing the todayLikeSet.
     */
    java.util.List<java.lang.Long> getTodayLikeSetList();
    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @return The count of todayLikeSet.
     */
    int getTodayLikeSetCount();
    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @param index The index of the element to return.
     * @return The todayLikeSet at the given index.
     */
    long getTodayLikeSet(int index);
  }
  /**
   * <pre>
   * 美观值信息
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_info_s2c}
   */
  public static final class charm_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_info_s2c)
      charm_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_info_s2c.newBuilder() to construct.
    private charm_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_info_s2c() {
      showMedalList_ = emptyIntList();
      receivedLevelRewardList_ = emptyIntList();
      unlockMedalList_ = emptyIntList();
      todayLikeSet_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.Builder.class);
    }

    public static final int CHARM_VALUE_FIELD_NUMBER = 1;
    private int charmValue_ = 0;
    /**
     * <pre>
     * 美观值
     * </pre>
     *
     * <code>uint32 charm_value = 1;</code>
     * @return The charmValue.
     */
    @java.lang.Override
    public int getCharmValue() {
      return charmValue_;
    }

    public static final int SHOW_MEDAL_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList showMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @return A list containing the showMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getShowMedalListList() {
      return showMedalList_;
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @return The count of showMedalList.
     */
    public int getShowMedalListCount() {
      return showMedalList_.size();
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 2;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    public int getShowMedalList(int index) {
      return showMedalList_.getInt(index);
    }
    private int showMedalListMemoizedSerializedSize = -1;

    public static final int RECEIVED_LEVEL_REWARD_LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList receivedLevelRewardList_ =
        emptyIntList();
    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @return A list containing the receivedLevelRewardList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getReceivedLevelRewardListList() {
      return receivedLevelRewardList_;
    }
    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @return The count of receivedLevelRewardList.
     */
    public int getReceivedLevelRewardListCount() {
      return receivedLevelRewardList_.size();
    }
    /**
     * <pre>
     * 已领取的美观等级奖励sn列表
     * </pre>
     *
     * <code>repeated uint32 received_level_reward_list = 3;</code>
     * @param index The index of the element to return.
     * @return The receivedLevelRewardList at the given index.
     */
    public int getReceivedLevelRewardList(int index) {
      return receivedLevelRewardList_.getInt(index);
    }
    private int receivedLevelRewardListMemoizedSerializedSize = -1;

    public static final int UNLOCK_MEDAL_LIST_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList unlockMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @return A list containing the unlockMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getUnlockMedalListList() {
      return unlockMedalList_;
    }
    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @return The count of unlockMedalList.
     */
    public int getUnlockMedalListCount() {
      return unlockMedalList_.size();
    }
    /**
     * <pre>
     * 已解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 unlock_medal_list = 4;</code>
     * @param index The index of the element to return.
     * @return The unlockMedalList at the given index.
     */
    public int getUnlockMedalList(int index) {
      return unlockMedalList_.getInt(index);
    }
    private int unlockMedalListMemoizedSerializedSize = -1;

    public static final int LIKE_TOTAL_NUM_FIELD_NUMBER = 5;
    private int likeTotalNum_ = 0;
    /**
     * <pre>
     * 收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 5;</code>
     * @return The likeTotalNum.
     */
    @java.lang.Override
    public int getLikeTotalNum() {
      return likeTotalNum_;
    }

    public static final int CLAIMED_LIKE_REWARD_NUM_FIELD_NUMBER = 6;
    private int claimedLikeRewardNum_ = 0;
    /**
     * <pre>
     * 今日我已领取的收藏室点赞奖励次数
     * </pre>
     *
     * <code>uint32 claimed_like_reward_num = 6;</code>
     * @return The claimedLikeRewardNum.
     */
    @java.lang.Override
    public int getClaimedLikeRewardNum() {
      return claimedLikeRewardNum_;
    }

    public static final int TODAY_LIKE_SET_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList todayLikeSet_ =
        emptyLongList();
    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @return A list containing the todayLikeSet.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getTodayLikeSetList() {
      return todayLikeSet_;
    }
    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @return The count of todayLikeSet.
     */
    public int getTodayLikeSetCount() {
      return todayLikeSet_.size();
    }
    /**
     * <pre>
     * 今日已点赞的玩家id集合
     * </pre>
     *
     * <code>repeated uint64 today_like_set = 7;</code>
     * @param index The index of the element to return.
     * @return The todayLikeSet at the given index.
     */
    public long getTodayLikeSet(int index) {
      return todayLikeSet_.getLong(index);
    }
    private int todayLikeSetMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (charmValue_ != 0) {
        output.writeUInt32(1, charmValue_);
      }
      if (getShowMedalListList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(showMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < showMedalList_.size(); i++) {
        output.writeUInt32NoTag(showMedalList_.getInt(i));
      }
      if (getReceivedLevelRewardListList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(receivedLevelRewardListMemoizedSerializedSize);
      }
      for (int i = 0; i < receivedLevelRewardList_.size(); i++) {
        output.writeUInt32NoTag(receivedLevelRewardList_.getInt(i));
      }
      if (getUnlockMedalListList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(unlockMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < unlockMedalList_.size(); i++) {
        output.writeUInt32NoTag(unlockMedalList_.getInt(i));
      }
      if (likeTotalNum_ != 0) {
        output.writeUInt32(5, likeTotalNum_);
      }
      if (claimedLikeRewardNum_ != 0) {
        output.writeUInt32(6, claimedLikeRewardNum_);
      }
      if (getTodayLikeSetList().size() > 0) {
        output.writeUInt32NoTag(58);
        output.writeUInt32NoTag(todayLikeSetMemoizedSerializedSize);
      }
      for (int i = 0; i < todayLikeSet_.size(); i++) {
        output.writeUInt64NoTag(todayLikeSet_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (charmValue_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, charmValue_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < showMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(showMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getShowMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        showMedalListMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receivedLevelRewardList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(receivedLevelRewardList_.getInt(i));
        }
        size += dataSize;
        if (!getReceivedLevelRewardListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receivedLevelRewardListMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < unlockMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(unlockMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getUnlockMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        unlockMedalListMemoizedSerializedSize = dataSize;
      }
      if (likeTotalNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, likeTotalNum_);
      }
      if (claimedLikeRewardNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, claimedLikeRewardNum_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < todayLikeSet_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(todayLikeSet_.getLong(i));
        }
        size += dataSize;
        if (!getTodayLikeSetList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        todayLikeSetMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c) obj;

      if (getCharmValue()
          != other.getCharmValue()) return false;
      if (!getShowMedalListList()
          .equals(other.getShowMedalListList())) return false;
      if (!getReceivedLevelRewardListList()
          .equals(other.getReceivedLevelRewardListList())) return false;
      if (!getUnlockMedalListList()
          .equals(other.getUnlockMedalListList())) return false;
      if (getLikeTotalNum()
          != other.getLikeTotalNum()) return false;
      if (getClaimedLikeRewardNum()
          != other.getClaimedLikeRewardNum()) return false;
      if (!getTodayLikeSetList()
          .equals(other.getTodayLikeSetList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHARM_VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getCharmValue();
      if (getShowMedalListCount() > 0) {
        hash = (37 * hash) + SHOW_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getShowMedalListList().hashCode();
      }
      if (getReceivedLevelRewardListCount() > 0) {
        hash = (37 * hash) + RECEIVED_LEVEL_REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getReceivedLevelRewardListList().hashCode();
      }
      if (getUnlockMedalListCount() > 0) {
        hash = (37 * hash) + UNLOCK_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getUnlockMedalListList().hashCode();
      }
      hash = (37 * hash) + LIKE_TOTAL_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getLikeTotalNum();
      hash = (37 * hash) + CLAIMED_LIKE_REWARD_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getClaimedLikeRewardNum();
      if (getTodayLikeSetCount() > 0) {
        hash = (37 * hash) + TODAY_LIKE_SET_FIELD_NUMBER;
        hash = (53 * hash) + getTodayLikeSetList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 美观值信息
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        charmValue_ = 0;
        showMedalList_ = emptyIntList();
        receivedLevelRewardList_ = emptyIntList();
        unlockMedalList_ = emptyIntList();
        likeTotalNum_ = 0;
        claimedLikeRewardNum_ = 0;
        todayLikeSet_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.charmValue_ = charmValue_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          showMedalList_.makeImmutable();
          result.showMedalList_ = showMedalList_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          receivedLevelRewardList_.makeImmutable();
          result.receivedLevelRewardList_ = receivedLevelRewardList_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          unlockMedalList_.makeImmutable();
          result.unlockMedalList_ = unlockMedalList_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.likeTotalNum_ = likeTotalNum_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.claimedLikeRewardNum_ = claimedLikeRewardNum_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          todayLikeSet_.makeImmutable();
          result.todayLikeSet_ = todayLikeSet_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c.getDefaultInstance()) return this;
        if (other.getCharmValue() != 0) {
          setCharmValue(other.getCharmValue());
        }
        if (!other.showMedalList_.isEmpty()) {
          if (showMedalList_.isEmpty()) {
            showMedalList_ = other.showMedalList_;
            showMedalList_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureShowMedalListIsMutable();
            showMedalList_.addAll(other.showMedalList_);
          }
          onChanged();
        }
        if (!other.receivedLevelRewardList_.isEmpty()) {
          if (receivedLevelRewardList_.isEmpty()) {
            receivedLevelRewardList_ = other.receivedLevelRewardList_;
            receivedLevelRewardList_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureReceivedLevelRewardListIsMutable();
            receivedLevelRewardList_.addAll(other.receivedLevelRewardList_);
          }
          onChanged();
        }
        if (!other.unlockMedalList_.isEmpty()) {
          if (unlockMedalList_.isEmpty()) {
            unlockMedalList_ = other.unlockMedalList_;
            unlockMedalList_.makeImmutable();
            bitField0_ |= 0x00000008;
          } else {
            ensureUnlockMedalListIsMutable();
            unlockMedalList_.addAll(other.unlockMedalList_);
          }
          onChanged();
        }
        if (other.getLikeTotalNum() != 0) {
          setLikeTotalNum(other.getLikeTotalNum());
        }
        if (other.getClaimedLikeRewardNum() != 0) {
          setClaimedLikeRewardNum(other.getClaimedLikeRewardNum());
        }
        if (!other.todayLikeSet_.isEmpty()) {
          if (todayLikeSet_.isEmpty()) {
            todayLikeSet_ = other.todayLikeSet_;
            todayLikeSet_.makeImmutable();
            bitField0_ |= 0x00000040;
          } else {
            ensureTodayLikeSetIsMutable();
            todayLikeSet_.addAll(other.todayLikeSet_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                charmValue_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureShowMedalListIsMutable();
                showMedalList_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureShowMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  showMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              case 24: {
                int v = input.readUInt32();
                ensureReceivedLevelRewardListIsMutable();
                receivedLevelRewardList_.addInt(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureReceivedLevelRewardListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  receivedLevelRewardList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 26
              case 32: {
                int v = input.readUInt32();
                ensureUnlockMedalListIsMutable();
                unlockMedalList_.addInt(v);
                break;
              } // case 32
              case 34: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureUnlockMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  unlockMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 34
              case 40: {
                likeTotalNum_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                claimedLikeRewardNum_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                long v = input.readUInt64();
                ensureTodayLikeSetIsMutable();
                todayLikeSet_.addLong(v);
                break;
              } // case 56
              case 58: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureTodayLikeSetIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  todayLikeSet_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 58
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int charmValue_ ;
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @return The charmValue.
       */
      @java.lang.Override
      public int getCharmValue() {
        return charmValue_;
      }
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @param value The charmValue to set.
       * @return This builder for chaining.
       */
      public Builder setCharmValue(int value) {

        charmValue_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharmValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        charmValue_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList showMedalList_ = emptyIntList();
      private void ensureShowMedalListIsMutable() {
        if (!showMedalList_.isModifiable()) {
          showMedalList_ = makeMutableCopy(showMedalList_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @return A list containing the showMedalList.
       */
      public java.util.List<java.lang.Integer>
          getShowMedalListList() {
        showMedalList_.makeImmutable();
        return showMedalList_;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @return The count of showMedalList.
       */
      public int getShowMedalListCount() {
        return showMedalList_.size();
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @param index The index of the element to return.
       * @return The showMedalList at the given index.
       */
      public int getShowMedalList(int index) {
        return showMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @param index The index to set the value at.
       * @param value The showMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setShowMedalList(
          int index, int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @param value The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addShowMedalList(int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @param values The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShowMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureShowMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, showMedalList_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowMedalList() {
        showMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList receivedLevelRewardList_ = emptyIntList();
      private void ensureReceivedLevelRewardListIsMutable() {
        if (!receivedLevelRewardList_.isModifiable()) {
          receivedLevelRewardList_ = makeMutableCopy(receivedLevelRewardList_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @return A list containing the receivedLevelRewardList.
       */
      public java.util.List<java.lang.Integer>
          getReceivedLevelRewardListList() {
        receivedLevelRewardList_.makeImmutable();
        return receivedLevelRewardList_;
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @return The count of receivedLevelRewardList.
       */
      public int getReceivedLevelRewardListCount() {
        return receivedLevelRewardList_.size();
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @param index The index of the element to return.
       * @return The receivedLevelRewardList at the given index.
       */
      public int getReceivedLevelRewardList(int index) {
        return receivedLevelRewardList_.getInt(index);
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @param index The index to set the value at.
       * @param value The receivedLevelRewardList to set.
       * @return This builder for chaining.
       */
      public Builder setReceivedLevelRewardList(
          int index, int value) {

        ensureReceivedLevelRewardListIsMutable();
        receivedLevelRewardList_.setInt(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @param value The receivedLevelRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addReceivedLevelRewardList(int value) {

        ensureReceivedLevelRewardListIsMutable();
        receivedLevelRewardList_.addInt(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @param values The receivedLevelRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addAllReceivedLevelRewardList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceivedLevelRewardListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receivedLevelRewardList_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的美观等级奖励sn列表
       * </pre>
       *
       * <code>repeated uint32 received_level_reward_list = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceivedLevelRewardList() {
        receivedLevelRewardList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList unlockMedalList_ = emptyIntList();
      private void ensureUnlockMedalListIsMutable() {
        if (!unlockMedalList_.isModifiable()) {
          unlockMedalList_ = makeMutableCopy(unlockMedalList_);
        }
        bitField0_ |= 0x00000008;
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @return A list containing the unlockMedalList.
       */
      public java.util.List<java.lang.Integer>
          getUnlockMedalListList() {
        unlockMedalList_.makeImmutable();
        return unlockMedalList_;
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @return The count of unlockMedalList.
       */
      public int getUnlockMedalListCount() {
        return unlockMedalList_.size();
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @param index The index of the element to return.
       * @return The unlockMedalList at the given index.
       */
      public int getUnlockMedalList(int index) {
        return unlockMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @param index The index to set the value at.
       * @param value The unlockMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setUnlockMedalList(
          int index, int value) {

        ensureUnlockMedalListIsMutable();
        unlockMedalList_.setInt(index, value);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @param value The unlockMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addUnlockMedalList(int value) {

        ensureUnlockMedalListIsMutable();
        unlockMedalList_.addInt(value);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @param values The unlockMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllUnlockMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureUnlockMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unlockMedalList_);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 unlock_medal_list = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnlockMedalList() {
        unlockMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }

      private int likeTotalNum_ ;
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 5;</code>
       * @return The likeTotalNum.
       */
      @java.lang.Override
      public int getLikeTotalNum() {
        return likeTotalNum_;
      }
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 5;</code>
       * @param value The likeTotalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLikeTotalNum(int value) {

        likeTotalNum_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000010);
        likeTotalNum_ = 0;
        onChanged();
        return this;
      }

      private int claimedLikeRewardNum_ ;
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 6;</code>
       * @return The claimedLikeRewardNum.
       */
      @java.lang.Override
      public int getClaimedLikeRewardNum() {
        return claimedLikeRewardNum_;
      }
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 6;</code>
       * @param value The claimedLikeRewardNum to set.
       * @return This builder for chaining.
       */
      public Builder setClaimedLikeRewardNum(int value) {

        claimedLikeRewardNum_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearClaimedLikeRewardNum() {
        bitField0_ = (bitField0_ & ~0x00000020);
        claimedLikeRewardNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList todayLikeSet_ = emptyLongList();
      private void ensureTodayLikeSetIsMutable() {
        if (!todayLikeSet_.isModifiable()) {
          todayLikeSet_ = makeMutableCopy(todayLikeSet_);
        }
        bitField0_ |= 0x00000040;
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @return A list containing the todayLikeSet.
       */
      public java.util.List<java.lang.Long>
          getTodayLikeSetList() {
        todayLikeSet_.makeImmutable();
        return todayLikeSet_;
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @return The count of todayLikeSet.
       */
      public int getTodayLikeSetCount() {
        return todayLikeSet_.size();
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @param index The index of the element to return.
       * @return The todayLikeSet at the given index.
       */
      public long getTodayLikeSet(int index) {
        return todayLikeSet_.getLong(index);
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @param index The index to set the value at.
       * @param value The todayLikeSet to set.
       * @return This builder for chaining.
       */
      public Builder setTodayLikeSet(
          int index, long value) {

        ensureTodayLikeSetIsMutable();
        todayLikeSet_.setLong(index, value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @param value The todayLikeSet to add.
       * @return This builder for chaining.
       */
      public Builder addTodayLikeSet(long value) {

        ensureTodayLikeSetIsMutable();
        todayLikeSet_.addLong(value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @param values The todayLikeSet to add.
       * @return This builder for chaining.
       */
      public Builder addAllTodayLikeSet(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureTodayLikeSetIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, todayLikeSet_);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日已点赞的玩家id集合
       * </pre>
       *
       * <code>repeated uint64 today_like_set = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearTodayLikeSet() {
        todayLikeSet_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_info_s2c>() {
      @java.lang.Override
      public charm_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_claim_level_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 美观等级表sn
     * </pre>
     *
     * <code>uint32 charm_sn = 1;</code>
     * @return The charmSn.
     */
    int getCharmSn();
  }
  /**
   * <pre>
   * 领取美观等级奖励
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s}
   */
  public static final class charm_claim_level_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s)
      charm_claim_level_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_claim_level_reward_c2s.newBuilder() to construct.
    private charm_claim_level_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_claim_level_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_claim_level_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.Builder.class);
    }

    public static final int CHARM_SN_FIELD_NUMBER = 1;
    private int charmSn_ = 0;
    /**
     * <pre>
     * 美观等级表sn
     * </pre>
     *
     * <code>uint32 charm_sn = 1;</code>
     * @return The charmSn.
     */
    @java.lang.Override
    public int getCharmSn() {
      return charmSn_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (charmSn_ != 0) {
        output.writeUInt32(1, charmSn_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (charmSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, charmSn_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s) obj;

      if (getCharmSn()
          != other.getCharmSn()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHARM_SN_FIELD_NUMBER;
      hash = (53 * hash) + getCharmSn();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 领取美观等级奖励
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        charmSn_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.charmSn_ = charmSn_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s.getDefaultInstance()) return this;
        if (other.getCharmSn() != 0) {
          setCharmSn(other.getCharmSn());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                charmSn_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int charmSn_ ;
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @return The charmSn.
       */
      @java.lang.Override
      public int getCharmSn() {
        return charmSn_;
      }
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @param value The charmSn to set.
       * @return This builder for chaining.
       */
      public Builder setCharmSn(int value) {

        charmSn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharmSn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        charmSn_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_claim_level_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_claim_level_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_claim_level_reward_c2s>() {
      @java.lang.Override
      public charm_claim_level_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_claim_level_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_claim_level_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_claim_level_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 美观等级表sn
     * </pre>
     *
     * <code>uint32 charm_sn = 1;</code>
     * @return The charmSn.
     */
    int getCharmSn();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c}
   */
  public static final class charm_claim_level_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c)
      charm_claim_level_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_claim_level_reward_s2c.newBuilder() to construct.
    private charm_claim_level_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_claim_level_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_claim_level_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.Builder.class);
    }

    public static final int CHARM_SN_FIELD_NUMBER = 1;
    private int charmSn_ = 0;
    /**
     * <pre>
     * 美观等级表sn
     * </pre>
     *
     * <code>uint32 charm_sn = 1;</code>
     * @return The charmSn.
     */
    @java.lang.Override
    public int getCharmSn() {
      return charmSn_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (charmSn_ != 0) {
        output.writeUInt32(1, charmSn_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (charmSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, charmSn_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c) obj;

      if (getCharmSn()
          != other.getCharmSn()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHARM_SN_FIELD_NUMBER;
      hash = (53 * hash) + getCharmSn();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        charmSn_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.charmSn_ = charmSn_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c.getDefaultInstance()) return this;
        if (other.getCharmSn() != 0) {
          setCharmSn(other.getCharmSn());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                charmSn_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int charmSn_ ;
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @return The charmSn.
       */
      @java.lang.Override
      public int getCharmSn() {
        return charmSn_;
      }
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @param value The charmSn to set.
       * @return This builder for chaining.
       */
      public Builder setCharmSn(int value) {

        charmSn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 美观等级表sn
       * </pre>
       *
       * <code>uint32 charm_sn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharmSn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        charmSn_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_claim_level_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_claim_level_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_claim_level_reward_s2c>() {
      @java.lang.Override
      public charm_claim_level_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_claim_level_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_claim_level_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_claim_level_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_save_show_medal_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return A list containing the showMedalList.
     */
    java.util.List<java.lang.Integer> getShowMedalListList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return The count of showMedalList.
     */
    int getShowMedalListCount();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    int getShowMedalList(int index);
  }
  /**
   * <pre>
   * 保存展示勋章列表
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s}
   */
  public static final class charm_save_show_medal_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s)
      charm_save_show_medal_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_save_show_medal_list_c2s.newBuilder() to construct.
    private charm_save_show_medal_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_save_show_medal_list_c2s() {
      showMedalList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_save_show_medal_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.Builder.class);
    }

    public static final int SHOW_MEDAL_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList showMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return A list containing the showMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getShowMedalListList() {
      return showMedalList_;
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return The count of showMedalList.
     */
    public int getShowMedalListCount() {
      return showMedalList_.size();
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    public int getShowMedalList(int index) {
      return showMedalList_.getInt(index);
    }
    private int showMedalListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getShowMedalListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(showMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < showMedalList_.size(); i++) {
        output.writeUInt32NoTag(showMedalList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < showMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(showMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getShowMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        showMedalListMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s) obj;

      if (!getShowMedalListList()
          .equals(other.getShowMedalListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getShowMedalListCount() > 0) {
        hash = (37 * hash) + SHOW_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getShowMedalListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 保存展示勋章列表
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        showMedalList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          showMedalList_.makeImmutable();
          result.showMedalList_ = showMedalList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s.getDefaultInstance()) return this;
        if (!other.showMedalList_.isEmpty()) {
          if (showMedalList_.isEmpty()) {
            showMedalList_ = other.showMedalList_;
            showMedalList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureShowMedalListIsMutable();
            showMedalList_.addAll(other.showMedalList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureShowMedalListIsMutable();
                showMedalList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureShowMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  showMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList showMedalList_ = emptyIntList();
      private void ensureShowMedalListIsMutable() {
        if (!showMedalList_.isModifiable()) {
          showMedalList_ = makeMutableCopy(showMedalList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return A list containing the showMedalList.
       */
      public java.util.List<java.lang.Integer>
          getShowMedalListList() {
        showMedalList_.makeImmutable();
        return showMedalList_;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return The count of showMedalList.
       */
      public int getShowMedalListCount() {
        return showMedalList_.size();
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param index The index of the element to return.
       * @return The showMedalList at the given index.
       */
      public int getShowMedalList(int index) {
        return showMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param index The index to set the value at.
       * @param value The showMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setShowMedalList(
          int index, int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param value The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addShowMedalList(int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param values The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShowMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureShowMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, showMedalList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowMedalList() {
        showMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_save_show_medal_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_save_show_medal_list_c2s>() {
      @java.lang.Override
      public charm_save_show_medal_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_save_show_medal_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_save_show_medal_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_save_show_medal_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return A list containing the showMedalList.
     */
    java.util.List<java.lang.Integer> getShowMedalListList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return The count of showMedalList.
     */
    int getShowMedalListCount();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    int getShowMedalList(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c}
   */
  public static final class charm_save_show_medal_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c)
      charm_save_show_medal_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_save_show_medal_list_s2c.newBuilder() to construct.
    private charm_save_show_medal_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_save_show_medal_list_s2c() {
      showMedalList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_save_show_medal_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.Builder.class);
    }

    public static final int SHOW_MEDAL_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList showMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return A list containing the showMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getShowMedalListList() {
      return showMedalList_;
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @return The count of showMedalList.
     */
    public int getShowMedalListCount() {
      return showMedalList_.size();
    }
    /**
     * <pre>
     * 展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 1;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    public int getShowMedalList(int index) {
      return showMedalList_.getInt(index);
    }
    private int showMedalListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getShowMedalListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(showMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < showMedalList_.size(); i++) {
        output.writeUInt32NoTag(showMedalList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < showMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(showMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getShowMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        showMedalListMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c) obj;

      if (!getShowMedalListList()
          .equals(other.getShowMedalListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getShowMedalListCount() > 0) {
        hash = (37 * hash) + SHOW_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getShowMedalListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        showMedalList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          showMedalList_.makeImmutable();
          result.showMedalList_ = showMedalList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c.getDefaultInstance()) return this;
        if (!other.showMedalList_.isEmpty()) {
          if (showMedalList_.isEmpty()) {
            showMedalList_ = other.showMedalList_;
            showMedalList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureShowMedalListIsMutable();
            showMedalList_.addAll(other.showMedalList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureShowMedalListIsMutable();
                showMedalList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureShowMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  showMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList showMedalList_ = emptyIntList();
      private void ensureShowMedalListIsMutable() {
        if (!showMedalList_.isModifiable()) {
          showMedalList_ = makeMutableCopy(showMedalList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return A list containing the showMedalList.
       */
      public java.util.List<java.lang.Integer>
          getShowMedalListList() {
        showMedalList_.makeImmutable();
        return showMedalList_;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return The count of showMedalList.
       */
      public int getShowMedalListCount() {
        return showMedalList_.size();
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param index The index of the element to return.
       * @return The showMedalList at the given index.
       */
      public int getShowMedalList(int index) {
        return showMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param index The index to set the value at.
       * @param value The showMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setShowMedalList(
          int index, int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param value The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addShowMedalList(int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @param values The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShowMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureShowMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, showMedalList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowMedalList() {
        showMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_save_show_medal_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_save_show_medal_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_save_show_medal_list_s2c>() {
      @java.lang.Override
      public charm_save_show_medal_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_save_show_medal_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_save_show_medal_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_show_medal_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_update_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_update_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 美观值
     * </pre>
     *
     * <code>uint32 charm_value = 1;</code>
     * @return The charmValue.
     */
    int getCharmValue();

    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @return A list containing the newUnlockMedalList.
     */
    java.util.List<java.lang.Integer> getNewUnlockMedalListList();
    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @return The count of newUnlockMedalList.
     */
    int getNewUnlockMedalListCount();
    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @param index The index of the element to return.
     * @return The newUnlockMedalList at the given index.
     */
    int getNewUnlockMedalList(int index);
  }
  /**
   * <pre>
   * 美观值信息更新
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_update_info_s2c}
   */
  public static final class charm_update_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_update_info_s2c)
      charm_update_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_update_info_s2c.newBuilder() to construct.
    private charm_update_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_update_info_s2c() {
      newUnlockMedalList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_update_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.Builder.class);
    }

    public static final int CHARM_VALUE_FIELD_NUMBER = 1;
    private int charmValue_ = 0;
    /**
     * <pre>
     * 美观值
     * </pre>
     *
     * <code>uint32 charm_value = 1;</code>
     * @return The charmValue.
     */
    @java.lang.Override
    public int getCharmValue() {
      return charmValue_;
    }

    public static final int NEW_UNLOCK_MEDAL_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList newUnlockMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @return A list containing the newUnlockMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getNewUnlockMedalListList() {
      return newUnlockMedalList_;
    }
    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @return The count of newUnlockMedalList.
     */
    public int getNewUnlockMedalListCount() {
      return newUnlockMedalList_.size();
    }
    /**
     * <pre>
     * 新解锁的勋章列表
     * </pre>
     *
     * <code>repeated uint32 new_unlock_medal_list = 2;</code>
     * @param index The index of the element to return.
     * @return The newUnlockMedalList at the given index.
     */
    public int getNewUnlockMedalList(int index) {
      return newUnlockMedalList_.getInt(index);
    }
    private int newUnlockMedalListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (charmValue_ != 0) {
        output.writeUInt32(1, charmValue_);
      }
      if (getNewUnlockMedalListList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(newUnlockMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < newUnlockMedalList_.size(); i++) {
        output.writeUInt32NoTag(newUnlockMedalList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (charmValue_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, charmValue_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < newUnlockMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(newUnlockMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getNewUnlockMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        newUnlockMedalListMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c) obj;

      if (getCharmValue()
          != other.getCharmValue()) return false;
      if (!getNewUnlockMedalListList()
          .equals(other.getNewUnlockMedalListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHARM_VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getCharmValue();
      if (getNewUnlockMedalListCount() > 0) {
        hash = (37 * hash) + NEW_UNLOCK_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getNewUnlockMedalListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 美观值信息更新
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_update_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_update_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        charmValue_ = 0;
        newUnlockMedalList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.charmValue_ = charmValue_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          newUnlockMedalList_.makeImmutable();
          result.newUnlockMedalList_ = newUnlockMedalList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c.getDefaultInstance()) return this;
        if (other.getCharmValue() != 0) {
          setCharmValue(other.getCharmValue());
        }
        if (!other.newUnlockMedalList_.isEmpty()) {
          if (newUnlockMedalList_.isEmpty()) {
            newUnlockMedalList_ = other.newUnlockMedalList_;
            newUnlockMedalList_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureNewUnlockMedalListIsMutable();
            newUnlockMedalList_.addAll(other.newUnlockMedalList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                charmValue_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureNewUnlockMedalListIsMutable();
                newUnlockMedalList_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureNewUnlockMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  newUnlockMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int charmValue_ ;
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @return The charmValue.
       */
      @java.lang.Override
      public int getCharmValue() {
        return charmValue_;
      }
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @param value The charmValue to set.
       * @return This builder for chaining.
       */
      public Builder setCharmValue(int value) {

        charmValue_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 美观值
       * </pre>
       *
       * <code>uint32 charm_value = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharmValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        charmValue_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList newUnlockMedalList_ = emptyIntList();
      private void ensureNewUnlockMedalListIsMutable() {
        if (!newUnlockMedalList_.isModifiable()) {
          newUnlockMedalList_ = makeMutableCopy(newUnlockMedalList_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @return A list containing the newUnlockMedalList.
       */
      public java.util.List<java.lang.Integer>
          getNewUnlockMedalListList() {
        newUnlockMedalList_.makeImmutable();
        return newUnlockMedalList_;
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @return The count of newUnlockMedalList.
       */
      public int getNewUnlockMedalListCount() {
        return newUnlockMedalList_.size();
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @param index The index of the element to return.
       * @return The newUnlockMedalList at the given index.
       */
      public int getNewUnlockMedalList(int index) {
        return newUnlockMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @param index The index to set the value at.
       * @param value The newUnlockMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setNewUnlockMedalList(
          int index, int value) {

        ensureNewUnlockMedalListIsMutable();
        newUnlockMedalList_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @param value The newUnlockMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addNewUnlockMedalList(int value) {

        ensureNewUnlockMedalListIsMutable();
        newUnlockMedalList_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @param values The newUnlockMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllNewUnlockMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureNewUnlockMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, newUnlockMedalList_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新解锁的勋章列表
       * </pre>
       *
       * <code>repeated uint32 new_unlock_medal_list = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewUnlockMedalList() {
        newUnlockMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_update_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_update_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_update_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_update_info_s2c>() {
      @java.lang.Override
      public charm_update_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_update_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_update_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_update_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_collection_room_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * <pre>
   * 收藏室信息
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s}
   */
  public static final class charm_collection_room_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s)
      charm_collection_room_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_collection_room_info_c2s.newBuilder() to construct.
    private charm_collection_room_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_collection_room_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_collection_room_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 收藏室信息
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_collection_room_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_collection_room_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_collection_room_info_c2s>() {
      @java.lang.Override
      public charm_collection_room_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_collection_room_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_collection_room_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_collection_room_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 目标玩家的名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * 目标玩家的名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     * @return Whether the head field is set.
     */
    boolean hasHead();
    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     * @return The head.
     */
    org.gof.demo.worldsrv.msg.Define.p_head getHead();
    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getHeadOrBuilder();

    /**
     * <pre>
     * 目标玩家的等级
     * </pre>
     *
     * <code>uint32 level = 4;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     * 目标玩家的美观值sn
     * </pre>
     *
     * <code>uint32 charm_sn = 5;</code>
     * @return The charmSn.
     */
    int getCharmSn();

    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @return A list containing the showMedalList.
     */
    java.util.List<java.lang.Integer> getShowMedalListList();
    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @return The count of showMedalList.
     */
    int getShowMedalListCount();
    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    int getShowMedalList(int index);

    /**
     * <pre>
     * 目标玩家收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 7;</code>
     * @return The likeTotalNum.
     */
    int getLikeTotalNum();

    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> 
        getRoleAreaListList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index);
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    int getRoleAreaListCount();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index);

    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> 
        getOtherAreaList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index);
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    int getOtherAreaCount();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index);

    /**
     * <pre>
     * 目标玩家的服务器id
     * </pre>
     *
     * <code>uint32 server_id = 10;</code>
     * @return The serverId.
     */
    int getServerId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c}
   */
  public static final class charm_collection_room_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c)
      charm_collection_room_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_collection_room_info_s2c.newBuilder() to construct.
    private charm_collection_room_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_collection_room_info_s2c() {
      name_ = "";
      showMedalList_ = emptyIntList();
      roleAreaList_ = java.util.Collections.emptyList();
      otherArea_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_collection_room_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object name_ = "";
    /**
     * <pre>
     * 目标玩家的名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 目标玩家的名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 3;
    private org.gof.demo.worldsrv.msg.Define.p_head head_;
    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     * @return Whether the head field is set.
     */
    @java.lang.Override
    public boolean hasHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     * @return The head.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_head getHead() {
      return head_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : head_;
    }
    /**
     * <pre>
     * 目标玩家的头像
     * </pre>
     *
     * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getHeadOrBuilder() {
      return head_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : head_;
    }

    public static final int LEVEL_FIELD_NUMBER = 4;
    private int level_ = 0;
    /**
     * <pre>
     * 目标玩家的等级
     * </pre>
     *
     * <code>uint32 level = 4;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int CHARM_SN_FIELD_NUMBER = 5;
    private int charmSn_ = 0;
    /**
     * <pre>
     * 目标玩家的美观值sn
     * </pre>
     *
     * <code>uint32 charm_sn = 5;</code>
     * @return The charmSn.
     */
    @java.lang.Override
    public int getCharmSn() {
      return charmSn_;
    }

    public static final int SHOW_MEDAL_LIST_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList showMedalList_ =
        emptyIntList();
    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @return A list containing the showMedalList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getShowMedalListList() {
      return showMedalList_;
    }
    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @return The count of showMedalList.
     */
    public int getShowMedalListCount() {
      return showMedalList_.size();
    }
    /**
     * <pre>
     * 目标玩家展示的勋章列表
     * </pre>
     *
     * <code>repeated uint32 show_medal_list = 6;</code>
     * @param index The index of the element to return.
     * @return The showMedalList at the given index.
     */
    public int getShowMedalList(int index) {
      return showMedalList_.getInt(index);
    }
    private int showMedalListMemoizedSerializedSize = -1;

    public static final int LIKE_TOTAL_NUM_FIELD_NUMBER = 7;
    private int likeTotalNum_ = 0;
    /**
     * <pre>
     * 目标玩家收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 7;</code>
     * @return The likeTotalNum.
     */
    @java.lang.Override
    public int getLikeTotalNum() {
      return likeTotalNum_;
    }

    public static final int ROLE_AREA_LIST_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_;
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    @java.lang.Override
    public int getRoleAreaListCount() {
      return roleAreaList_.size();
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
      return roleAreaList_.get(index);
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index) {
      return roleAreaList_.get(index);
    }

    public static final int OTHER_AREA_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_;
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    @java.lang.Override
    public int getOtherAreaCount() {
      return otherArea_.size();
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
      return otherArea_.get(index);
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index) {
      return otherArea_.get(index);
    }

    public static final int SERVER_ID_FIELD_NUMBER = 10;
    private int serverId_ = 0;
    /**
     * <pre>
     * 目标玩家的服务器id
     * </pre>
     *
     * <code>uint32 server_id = 10;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getHead());
      }
      if (level_ != 0) {
        output.writeUInt32(4, level_);
      }
      if (charmSn_ != 0) {
        output.writeUInt32(5, charmSn_);
      }
      if (getShowMedalListList().size() > 0) {
        output.writeUInt32NoTag(50);
        output.writeUInt32NoTag(showMedalListMemoizedSerializedSize);
      }
      for (int i = 0; i < showMedalList_.size(); i++) {
        output.writeUInt32NoTag(showMedalList_.getInt(i));
      }
      if (likeTotalNum_ != 0) {
        output.writeUInt32(7, likeTotalNum_);
      }
      for (int i = 0; i < roleAreaList_.size(); i++) {
        output.writeMessage(8, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        output.writeMessage(9, otherArea_.get(i));
      }
      if (serverId_ != 0) {
        output.writeUInt32(10, serverId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getHead());
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, level_);
      }
      if (charmSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, charmSn_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < showMedalList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(showMedalList_.getInt(i));
        }
        size += dataSize;
        if (!getShowMedalListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        showMedalListMemoizedSerializedSize = dataSize;
      }
      if (likeTotalNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, likeTotalNum_);
      }
      for (int i = 0; i < roleAreaList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, otherArea_.get(i));
      }
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, serverId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (hasHead() != other.hasHead()) return false;
      if (hasHead()) {
        if (!getHead()
            .equals(other.getHead())) return false;
      }
      if (getLevel()
          != other.getLevel()) return false;
      if (getCharmSn()
          != other.getCharmSn()) return false;
      if (!getShowMedalListList()
          .equals(other.getShowMedalListList())) return false;
      if (getLikeTotalNum()
          != other.getLikeTotalNum()) return false;
      if (!getRoleAreaListList()
          .equals(other.getRoleAreaListList())) return false;
      if (!getOtherAreaList()
          .equals(other.getOtherAreaList())) return false;
      if (getServerId()
          != other.getServerId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      if (hasHead()) {
        hash = (37 * hash) + HEAD_FIELD_NUMBER;
        hash = (53 * hash) + getHead().hashCode();
      }
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + CHARM_SN_FIELD_NUMBER;
      hash = (53 * hash) + getCharmSn();
      if (getShowMedalListCount() > 0) {
        hash = (37 * hash) + SHOW_MEDAL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getShowMedalListList().hashCode();
      }
      hash = (37 * hash) + LIKE_TOTAL_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getLikeTotalNum();
      if (getRoleAreaListCount() > 0) {
        hash = (37 * hash) + ROLE_AREA_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRoleAreaListList().hashCode();
      }
      if (getOtherAreaCount() > 0) {
        hash = (37 * hash) + OTHER_AREA_FIELD_NUMBER;
        hash = (53 * hash) + getOtherAreaList().hashCode();
      }
      hash = (37 * hash) + SERVER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeadFieldBuilder();
          getRoleAreaListFieldBuilder();
          getOtherAreaFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        name_ = "";
        head_ = null;
        if (headBuilder_ != null) {
          headBuilder_.dispose();
          headBuilder_ = null;
        }
        level_ = 0;
        charmSn_ = 0;
        showMedalList_ = emptyIntList();
        likeTotalNum_ = 0;
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
        } else {
          roleAreaList_ = null;
          roleAreaListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
        } else {
          otherArea_ = null;
          otherAreaBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        serverId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c result) {
        if (roleAreaListBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0)) {
            roleAreaList_ = java.util.Collections.unmodifiableList(roleAreaList_);
            bitField0_ = (bitField0_ & ~0x00000080);
          }
          result.roleAreaList_ = roleAreaList_;
        } else {
          result.roleAreaList_ = roleAreaListBuilder_.build();
        }
        if (otherAreaBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0)) {
            otherArea_ = java.util.Collections.unmodifiableList(otherArea_);
            bitField0_ = (bitField0_ & ~0x00000100);
          }
          result.otherArea_ = otherArea_;
        } else {
          result.otherArea_ = otherAreaBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.head_ = headBuilder_ == null
              ? head_
              : headBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.level_ = level_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.charmSn_ = charmSn_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          showMedalList_.makeImmutable();
          result.showMedalList_ = showMedalList_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.likeTotalNum_ = likeTotalNum_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.serverId_ = serverId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasHead()) {
          mergeHead(other.getHead());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getCharmSn() != 0) {
          setCharmSn(other.getCharmSn());
        }
        if (!other.showMedalList_.isEmpty()) {
          if (showMedalList_.isEmpty()) {
            showMedalList_ = other.showMedalList_;
            showMedalList_.makeImmutable();
            bitField0_ |= 0x00000020;
          } else {
            ensureShowMedalListIsMutable();
            showMedalList_.addAll(other.showMedalList_);
          }
          onChanged();
        }
        if (other.getLikeTotalNum() != 0) {
          setLikeTotalNum(other.getLikeTotalNum());
        }
        if (roleAreaListBuilder_ == null) {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaList_.isEmpty()) {
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000080);
            } else {
              ensureRoleAreaListIsMutable();
              roleAreaList_.addAll(other.roleAreaList_);
            }
            onChanged();
          }
        } else {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaListBuilder_.isEmpty()) {
              roleAreaListBuilder_.dispose();
              roleAreaListBuilder_ = null;
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000080);
              roleAreaListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRoleAreaListFieldBuilder() : null;
            } else {
              roleAreaListBuilder_.addAllMessages(other.roleAreaList_);
            }
          }
        }
        if (otherAreaBuilder_ == null) {
          if (!other.otherArea_.isEmpty()) {
            if (otherArea_.isEmpty()) {
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000100);
            } else {
              ensureOtherAreaIsMutable();
              otherArea_.addAll(other.otherArea_);
            }
            onChanged();
          }
        } else {
          if (!other.otherArea_.isEmpty()) {
            if (otherAreaBuilder_.isEmpty()) {
              otherAreaBuilder_.dispose();
              otherAreaBuilder_ = null;
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000100);
              otherAreaBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOtherAreaFieldBuilder() : null;
            } else {
              otherAreaBuilder_.addAllMessages(other.otherArea_);
            }
          }
        }
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getHeadFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                level_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                charmSn_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                int v = input.readUInt32();
                ensureShowMedalListIsMutable();
                showMedalList_.addInt(v);
                break;
              } // case 48
              case 50: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureShowMedalListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  showMedalList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 50
              case 56: {
                likeTotalNum_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                org.gof.demo.worldsrv.msg.Define.p_charm_role_area m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_role_area.parser(),
                        extensionRegistry);
                if (roleAreaListBuilder_ == null) {
                  ensureRoleAreaListIsMutable();
                  roleAreaList_.add(m);
                } else {
                  roleAreaListBuilder_.addMessage(m);
                }
                break;
              } // case 66
              case 74: {
                org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.parser(),
                        extensionRegistry);
                if (otherAreaBuilder_ == null) {
                  ensureOtherAreaIsMutable();
                  otherArea_.add(m);
                } else {
                  otherAreaBuilder_.addMessage(m);
                }
                break;
              } // case 74
              case 80: {
                serverId_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * 目标玩家的名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 目标玩家的名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 目标玩家的名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_head head_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> headBuilder_;
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       * @return Whether the head field is set.
       */
      public boolean hasHead() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       * @return The head.
       */
      public org.gof.demo.worldsrv.msg.Define.p_head getHead() {
        if (headBuilder_ == null) {
          return head_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : head_;
        } else {
          return headBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public Builder setHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (headBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          head_ = value;
        } else {
          headBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public Builder setHead(
          org.gof.demo.worldsrv.msg.Define.p_head.Builder builderForValue) {
        if (headBuilder_ == null) {
          head_ = builderForValue.build();
        } else {
          headBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public Builder mergeHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (headBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            head_ != null &&
            head_ != org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance()) {
            getHeadBuilder().mergeFrom(value);
          } else {
            head_ = value;
          }
        } else {
          headBuilder_.mergeFrom(value);
        }
        if (head_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public Builder clearHead() {
        bitField0_ = (bitField0_ & ~0x00000004);
        head_ = null;
        if (headBuilder_ != null) {
          headBuilder_.dispose();
          headBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_head.Builder getHeadBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getHeadOrBuilder() {
        if (headBuilder_ != null) {
          return headBuilder_.getMessageOrBuilder();
        } else {
          return head_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : head_;
        }
      }
      /**
       * <pre>
       * 目标玩家的头像
       * </pre>
       *
       * <code>.org.gof.demo.worldsrv.msg.p_head head = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> 
          getHeadFieldBuilder() {
        if (headBuilder_ == null) {
          headBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder>(
                  getHead(),
                  getParentForChildren(),
                  isClean());
          head_ = null;
        }
        return headBuilder_;
      }

      private int level_ ;
      /**
       * <pre>
       * 目标玩家的等级
       * </pre>
       *
       * <code>uint32 level = 4;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 目标玩家的等级
       * </pre>
       *
       * <code>uint32 level = 4;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {

        level_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的等级
       * </pre>
       *
       * <code>uint32 level = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        level_ = 0;
        onChanged();
        return this;
      }

      private int charmSn_ ;
      /**
       * <pre>
       * 目标玩家的美观值sn
       * </pre>
       *
       * <code>uint32 charm_sn = 5;</code>
       * @return The charmSn.
       */
      @java.lang.Override
      public int getCharmSn() {
        return charmSn_;
      }
      /**
       * <pre>
       * 目标玩家的美观值sn
       * </pre>
       *
       * <code>uint32 charm_sn = 5;</code>
       * @param value The charmSn to set.
       * @return This builder for chaining.
       */
      public Builder setCharmSn(int value) {

        charmSn_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的美观值sn
       * </pre>
       *
       * <code>uint32 charm_sn = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharmSn() {
        bitField0_ = (bitField0_ & ~0x00000010);
        charmSn_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList showMedalList_ = emptyIntList();
      private void ensureShowMedalListIsMutable() {
        if (!showMedalList_.isModifiable()) {
          showMedalList_ = makeMutableCopy(showMedalList_);
        }
        bitField0_ |= 0x00000020;
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @return A list containing the showMedalList.
       */
      public java.util.List<java.lang.Integer>
          getShowMedalListList() {
        showMedalList_.makeImmutable();
        return showMedalList_;
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @return The count of showMedalList.
       */
      public int getShowMedalListCount() {
        return showMedalList_.size();
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @param index The index of the element to return.
       * @return The showMedalList at the given index.
       */
      public int getShowMedalList(int index) {
        return showMedalList_.getInt(index);
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @param index The index to set the value at.
       * @param value The showMedalList to set.
       * @return This builder for chaining.
       */
      public Builder setShowMedalList(
          int index, int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.setInt(index, value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @param value The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addShowMedalList(int value) {

        ensureShowMedalListIsMutable();
        showMedalList_.addInt(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @param values The showMedalList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShowMedalList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureShowMedalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, showMedalList_);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家展示的勋章列表
       * </pre>
       *
       * <code>repeated uint32 show_medal_list = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowMedalList() {
        showMedalList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }

      private int likeTotalNum_ ;
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 7;</code>
       * @return The likeTotalNum.
       */
      @java.lang.Override
      public int getLikeTotalNum() {
        return likeTotalNum_;
      }
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 7;</code>
       * @param value The likeTotalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLikeTotalNum(int value) {

        likeTotalNum_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000040);
        likeTotalNum_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_ =
        java.util.Collections.emptyList();
      private void ensureRoleAreaListIsMutable() {
        if (!((bitField0_ & 0x00000080) != 0)) {
          roleAreaList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_role_area>(roleAreaList_);
          bitField0_ |= 0x00000080;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> roleAreaListBuilder_;

      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
        if (roleAreaListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        } else {
          return roleAreaListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public int getRoleAreaListCount() {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.size();
        } else {
          return roleAreaListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);
        } else {
          return roleAreaListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder addRoleAreaList(org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder addRoleAreaList(
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder addAllRoleAreaList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_area> values) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roleAreaList_);
          onChanged();
        } else {
          roleAreaListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder clearRoleAreaList() {
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
        } else {
          roleAreaListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public Builder removeRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.remove(index);
          onChanged();
        } else {
          roleAreaListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder getRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
          int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);  } else {
          return roleAreaListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
           getRoleAreaListOrBuilderList() {
        if (roleAreaListBuilder_ != null) {
          return roleAreaListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder() {
        return getRoleAreaListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 8;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder> 
           getRoleAreaListBuilderList() {
        return getRoleAreaListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
          getRoleAreaListFieldBuilder() {
        if (roleAreaListBuilder_ == null) {
          roleAreaListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder>(
                  roleAreaList_,
                  ((bitField0_ & 0x00000080) != 0),
                  getParentForChildren(),
                  isClean());
          roleAreaList_ = null;
        }
        return roleAreaListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_ =
        java.util.Collections.emptyList();
      private void ensureOtherAreaIsMutable() {
        if (!((bitField0_ & 0x00000100) != 0)) {
          otherArea_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item>(otherArea_);
          bitField0_ |= 0x00000100;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> otherAreaBuilder_;

      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
        if (otherAreaBuilder_ == null) {
          return java.util.Collections.unmodifiableList(otherArea_);
        } else {
          return otherAreaBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public int getOtherAreaCount() {
        if (otherAreaBuilder_ == null) {
          return otherArea_.size();
        } else {
          return otherAreaBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);
        } else {
          return otherAreaBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.set(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.set(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder addOtherArea(org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder addOtherArea(
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder addAllOtherArea(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> values) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, otherArea_);
          onChanged();
        } else {
          otherAreaBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder clearOtherArea() {
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
        } else {
          otherAreaBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public Builder removeOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.remove(index);
          onChanged();
        } else {
          otherAreaBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder getOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
          int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);  } else {
          return otherAreaBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
           getOtherAreaOrBuilderList() {
        if (otherAreaBuilder_ != null) {
          return otherAreaBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(otherArea_);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder() {
        return getOtherAreaFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 9;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder> 
           getOtherAreaBuilderList() {
        return getOtherAreaFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
          getOtherAreaFieldBuilder() {
        if (otherAreaBuilder_ == null) {
          otherAreaBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder>(
                  otherArea_,
                  ((bitField0_ & 0x00000100) != 0),
                  getParentForChildren(),
                  isClean());
          otherArea_ = null;
        }
        return otherAreaBuilder_;
      }

      private int serverId_ ;
      /**
       * <pre>
       * 目标玩家的服务器id
       * </pre>
       *
       * <code>uint32 server_id = 10;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <pre>
       * 目标玩家的服务器id
       * </pre>
       *
       * <code>uint32 server_id = 10;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {

        serverId_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家的服务器id
       * </pre>
       *
       * <code>uint32 server_id = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        bitField0_ = (bitField0_ & ~0x00000200);
        serverId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_collection_room_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_collection_room_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_collection_room_info_s2c>() {
      @java.lang.Override
      public charm_collection_room_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_collection_room_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_collection_room_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_save_display_area_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_save_display_area_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> 
        getRoleAreaListList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index);
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    int getRoleAreaListCount();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index);

    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> 
        getOtherAreaList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index);
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    int getOtherAreaCount();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 保存展示区
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_display_area_c2s}
   */
  public static final class charm_save_display_area_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_save_display_area_c2s)
      charm_save_display_area_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_save_display_area_c2s.newBuilder() to construct.
    private charm_save_display_area_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_save_display_area_c2s() {
      roleAreaList_ = java.util.Collections.emptyList();
      otherArea_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_save_display_area_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.Builder.class);
    }

    public static final int ROLE_AREA_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_;
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public int getRoleAreaListCount() {
      return roleAreaList_.size();
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
      return roleAreaList_.get(index);
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index) {
      return roleAreaList_.get(index);
    }

    public static final int OTHER_AREA_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_;
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public int getOtherAreaCount() {
      return otherArea_.size();
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
      return otherArea_.get(index);
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index) {
      return otherArea_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < roleAreaList_.size(); i++) {
        output.writeMessage(1, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        output.writeMessage(2, otherArea_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < roleAreaList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, otherArea_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s) obj;

      if (!getRoleAreaListList()
          .equals(other.getRoleAreaListList())) return false;
      if (!getOtherAreaList()
          .equals(other.getOtherAreaList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRoleAreaListCount() > 0) {
        hash = (37 * hash) + ROLE_AREA_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRoleAreaListList().hashCode();
      }
      if (getOtherAreaCount() > 0) {
        hash = (37 * hash) + OTHER_AREA_FIELD_NUMBER;
        hash = (53 * hash) + getOtherAreaList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 保存展示区
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_display_area_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_save_display_area_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
        } else {
          roleAreaList_ = null;
          roleAreaListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
        } else {
          otherArea_ = null;
          otherAreaBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s result) {
        if (roleAreaListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            roleAreaList_ = java.util.Collections.unmodifiableList(roleAreaList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.roleAreaList_ = roleAreaList_;
        } else {
          result.roleAreaList_ = roleAreaListBuilder_.build();
        }
        if (otherAreaBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            otherArea_ = java.util.Collections.unmodifiableList(otherArea_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.otherArea_ = otherArea_;
        } else {
          result.otherArea_ = otherAreaBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s.getDefaultInstance()) return this;
        if (roleAreaListBuilder_ == null) {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaList_.isEmpty()) {
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRoleAreaListIsMutable();
              roleAreaList_.addAll(other.roleAreaList_);
            }
            onChanged();
          }
        } else {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaListBuilder_.isEmpty()) {
              roleAreaListBuilder_.dispose();
              roleAreaListBuilder_ = null;
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              roleAreaListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRoleAreaListFieldBuilder() : null;
            } else {
              roleAreaListBuilder_.addAllMessages(other.roleAreaList_);
            }
          }
        }
        if (otherAreaBuilder_ == null) {
          if (!other.otherArea_.isEmpty()) {
            if (otherArea_.isEmpty()) {
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureOtherAreaIsMutable();
              otherArea_.addAll(other.otherArea_);
            }
            onChanged();
          }
        } else {
          if (!other.otherArea_.isEmpty()) {
            if (otherAreaBuilder_.isEmpty()) {
              otherAreaBuilder_.dispose();
              otherAreaBuilder_ = null;
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000002);
              otherAreaBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOtherAreaFieldBuilder() : null;
            } else {
              otherAreaBuilder_.addAllMessages(other.otherArea_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_charm_role_area m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_role_area.parser(),
                        extensionRegistry);
                if (roleAreaListBuilder_ == null) {
                  ensureRoleAreaListIsMutable();
                  roleAreaList_.add(m);
                } else {
                  roleAreaListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.parser(),
                        extensionRegistry);
                if (otherAreaBuilder_ == null) {
                  ensureOtherAreaIsMutable();
                  otherArea_.add(m);
                } else {
                  otherAreaBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_ =
        java.util.Collections.emptyList();
      private void ensureRoleAreaListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          roleAreaList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_role_area>(roleAreaList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> roleAreaListBuilder_;

      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
        if (roleAreaListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        } else {
          return roleAreaListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public int getRoleAreaListCount() {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.size();
        } else {
          return roleAreaListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);
        } else {
          return roleAreaListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addAllRoleAreaList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_area> values) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roleAreaList_);
          onChanged();
        } else {
          roleAreaListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder clearRoleAreaList() {
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          roleAreaListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder removeRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.remove(index);
          onChanged();
        } else {
          roleAreaListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder getRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
          int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);  } else {
          return roleAreaListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
           getRoleAreaListOrBuilderList() {
        if (roleAreaListBuilder_ != null) {
          return roleAreaListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder() {
        return getRoleAreaListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder> 
           getRoleAreaListBuilderList() {
        return getRoleAreaListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
          getRoleAreaListFieldBuilder() {
        if (roleAreaListBuilder_ == null) {
          roleAreaListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder>(
                  roleAreaList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          roleAreaList_ = null;
        }
        return roleAreaListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_ =
        java.util.Collections.emptyList();
      private void ensureOtherAreaIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          otherArea_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item>(otherArea_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> otherAreaBuilder_;

      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
        if (otherAreaBuilder_ == null) {
          return java.util.Collections.unmodifiableList(otherArea_);
        } else {
          return otherAreaBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public int getOtherAreaCount() {
        if (otherAreaBuilder_ == null) {
          return otherArea_.size();
        } else {
          return otherAreaBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);
        } else {
          return otherAreaBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.set(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.set(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addAllOtherArea(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> values) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, otherArea_);
          onChanged();
        } else {
          otherAreaBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder clearOtherArea() {
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          otherAreaBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder removeOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.remove(index);
          onChanged();
        } else {
          otherAreaBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder getOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
          int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);  } else {
          return otherAreaBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
           getOtherAreaOrBuilderList() {
        if (otherAreaBuilder_ != null) {
          return otherAreaBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(otherArea_);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder() {
        return getOtherAreaFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder> 
           getOtherAreaBuilderList() {
        return getOtherAreaFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
          getOtherAreaFieldBuilder() {
        if (otherAreaBuilder_ == null) {
          otherAreaBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder>(
                  otherArea_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          otherArea_ = null;
        }
        return otherAreaBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_save_display_area_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_save_display_area_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_save_display_area_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_save_display_area_c2s>() {
      @java.lang.Override
      public charm_save_display_area_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_save_display_area_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_save_display_area_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_save_display_area_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_save_display_area_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> 
        getRoleAreaListList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index);
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    int getRoleAreaListCount();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList();
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index);

    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> 
        getOtherAreaList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index);
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    int getOtherAreaCount();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList();
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_display_area_s2c}
   */
  public static final class charm_save_display_area_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_save_display_area_s2c)
      charm_save_display_area_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_save_display_area_s2c.newBuilder() to construct.
    private charm_save_display_area_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_save_display_area_s2c() {
      roleAreaList_ = java.util.Collections.emptyList();
      otherArea_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_save_display_area_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.Builder.class);
    }

    public static final int ROLE_AREA_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_;
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
        getRoleAreaListOrBuilderList() {
      return roleAreaList_;
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public int getRoleAreaListCount() {
      return roleAreaList_.size();
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
      return roleAreaList_.get(index);
    }
    /**
     * <pre>
     * 主角外观展示区列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
        int index) {
      return roleAreaList_.get(index);
    }

    public static final int OTHER_AREA_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_;
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
        getOtherAreaOrBuilderList() {
      return otherArea_;
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public int getOtherAreaCount() {
      return otherArea_.size();
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
      return otherArea_.get(index);
    }
    /**
     * <pre>
     * 其他外观展示区
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
        int index) {
      return otherArea_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < roleAreaList_.size(); i++) {
        output.writeMessage(1, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        output.writeMessage(2, otherArea_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < roleAreaList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, roleAreaList_.get(i));
      }
      for (int i = 0; i < otherArea_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, otherArea_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c) obj;

      if (!getRoleAreaListList()
          .equals(other.getRoleAreaListList())) return false;
      if (!getOtherAreaList()
          .equals(other.getOtherAreaList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRoleAreaListCount() > 0) {
        hash = (37 * hash) + ROLE_AREA_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRoleAreaListList().hashCode();
      }
      if (getOtherAreaCount() > 0) {
        hash = (37 * hash) + OTHER_AREA_FIELD_NUMBER;
        hash = (53 * hash) + getOtherAreaList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_save_display_area_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_save_display_area_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
        } else {
          roleAreaList_ = null;
          roleAreaListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
        } else {
          otherArea_ = null;
          otherAreaBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c result) {
        if (roleAreaListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            roleAreaList_ = java.util.Collections.unmodifiableList(roleAreaList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.roleAreaList_ = roleAreaList_;
        } else {
          result.roleAreaList_ = roleAreaListBuilder_.build();
        }
        if (otherAreaBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            otherArea_ = java.util.Collections.unmodifiableList(otherArea_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.otherArea_ = otherArea_;
        } else {
          result.otherArea_ = otherAreaBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c.getDefaultInstance()) return this;
        if (roleAreaListBuilder_ == null) {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaList_.isEmpty()) {
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRoleAreaListIsMutable();
              roleAreaList_.addAll(other.roleAreaList_);
            }
            onChanged();
          }
        } else {
          if (!other.roleAreaList_.isEmpty()) {
            if (roleAreaListBuilder_.isEmpty()) {
              roleAreaListBuilder_.dispose();
              roleAreaListBuilder_ = null;
              roleAreaList_ = other.roleAreaList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              roleAreaListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRoleAreaListFieldBuilder() : null;
            } else {
              roleAreaListBuilder_.addAllMessages(other.roleAreaList_);
            }
          }
        }
        if (otherAreaBuilder_ == null) {
          if (!other.otherArea_.isEmpty()) {
            if (otherArea_.isEmpty()) {
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureOtherAreaIsMutable();
              otherArea_.addAll(other.otherArea_);
            }
            onChanged();
          }
        } else {
          if (!other.otherArea_.isEmpty()) {
            if (otherAreaBuilder_.isEmpty()) {
              otherAreaBuilder_.dispose();
              otherAreaBuilder_ = null;
              otherArea_ = other.otherArea_;
              bitField0_ = (bitField0_ & ~0x00000002);
              otherAreaBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOtherAreaFieldBuilder() : null;
            } else {
              otherAreaBuilder_.addAllMessages(other.otherArea_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_charm_role_area m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_role_area.parser(),
                        extensionRegistry);
                if (roleAreaListBuilder_ == null) {
                  ensureRoleAreaListIsMutable();
                  roleAreaList_.add(m);
                } else {
                  roleAreaListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.parser(),
                        extensionRegistry);
                if (otherAreaBuilder_ == null) {
                  ensureOtherAreaIsMutable();
                  otherArea_.add(m);
                } else {
                  otherAreaBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> roleAreaList_ =
        java.util.Collections.emptyList();
      private void ensureRoleAreaListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          roleAreaList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_role_area>(roleAreaList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> roleAreaListBuilder_;

      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area> getRoleAreaListList() {
        if (roleAreaListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        } else {
          return roleAreaListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public int getRoleAreaListCount() {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.size();
        } else {
          return roleAreaListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area getRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);
        } else {
          return roleAreaListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder setRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area value) {
        if (roleAreaListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, value);
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addRoleAreaList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder builderForValue) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleAreaListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder addAllRoleAreaList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_area> values) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roleAreaList_);
          onChanged();
        } else {
          roleAreaListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder clearRoleAreaList() {
        if (roleAreaListBuilder_ == null) {
          roleAreaList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          roleAreaListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public Builder removeRoleAreaList(int index) {
        if (roleAreaListBuilder_ == null) {
          ensureRoleAreaListIsMutable();
          roleAreaList_.remove(index);
          onChanged();
        } else {
          roleAreaListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder getRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder getRoleAreaListOrBuilder(
          int index) {
        if (roleAreaListBuilder_ == null) {
          return roleAreaList_.get(index);  } else {
          return roleAreaListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
           getRoleAreaListOrBuilderList() {
        if (roleAreaListBuilder_ != null) {
          return roleAreaListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roleAreaList_);
        }
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder() {
        return getRoleAreaListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder addRoleAreaListBuilder(
          int index) {
        return getRoleAreaListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.getDefaultInstance());
      }
      /**
       * <pre>
       * 主角外观展示区列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_role_area role_area_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder> 
           getRoleAreaListBuilderList() {
        return getRoleAreaListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder> 
          getRoleAreaListFieldBuilder() {
        if (roleAreaListBuilder_ == null) {
          roleAreaListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_role_area, org.gof.demo.worldsrv.msg.Define.p_charm_role_area.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_role_areaOrBuilder>(
                  roleAreaList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          roleAreaList_ = null;
        }
        return roleAreaListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> otherArea_ =
        java.util.Collections.emptyList();
      private void ensureOtherAreaIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          otherArea_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item>(otherArea_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> otherAreaBuilder_;

      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> getOtherAreaList() {
        if (otherAreaBuilder_ == null) {
          return java.util.Collections.unmodifiableList(otherArea_);
        } else {
          return otherAreaBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public int getOtherAreaCount() {
        if (otherAreaBuilder_ == null) {
          return otherArea_.size();
        } else {
          return otherAreaBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item getOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);
        } else {
          return otherAreaBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.set(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder setOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.set(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item value) {
        if (otherAreaBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOtherAreaIsMutable();
          otherArea_.add(index, value);
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addOtherArea(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder builderForValue) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.add(index, builderForValue.build());
          onChanged();
        } else {
          otherAreaBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder addAllOtherArea(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item> values) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, otherArea_);
          onChanged();
        } else {
          otherAreaBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder clearOtherArea() {
        if (otherAreaBuilder_ == null) {
          otherArea_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          otherAreaBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public Builder removeOtherArea(int index) {
        if (otherAreaBuilder_ == null) {
          ensureOtherAreaIsMutable();
          otherArea_.remove(index);
          onChanged();
        } else {
          otherAreaBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder getOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder getOtherAreaOrBuilder(
          int index) {
        if (otherAreaBuilder_ == null) {
          return otherArea_.get(index);  } else {
          return otherAreaBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
           getOtherAreaOrBuilderList() {
        if (otherAreaBuilder_ != null) {
          return otherAreaBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(otherArea_);
        }
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder() {
        return getOtherAreaFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder addOtherAreaBuilder(
          int index) {
        return getOtherAreaFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.getDefaultInstance());
      }
      /**
       * <pre>
       * 其他外观展示区
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_other_area_item other_area = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder> 
           getOtherAreaBuilderList() {
        return getOtherAreaFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder> 
          getOtherAreaFieldBuilder() {
        if (otherAreaBuilder_ == null) {
          otherAreaBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_item.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_other_area_itemOrBuilder>(
                  otherArea_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          otherArea_ = null;
        }
        return otherAreaBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_save_display_area_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_save_display_area_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_save_display_area_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_save_display_area_s2c>() {
      @java.lang.Override
      public charm_save_display_area_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_save_display_area_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_save_display_area_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_save_display_area_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_like_collection_room_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * <pre>
   * 点赞收藏室
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s}
   */
  public static final class charm_like_collection_room_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s)
      charm_like_collection_room_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_like_collection_room_c2s.newBuilder() to construct.
    private charm_like_collection_room_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_like_collection_room_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_like_collection_room_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 点赞收藏室
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_like_collection_room_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_like_collection_room_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_like_collection_room_c2s>() {
      @java.lang.Override
      public charm_like_collection_room_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_like_collection_room_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_like_collection_room_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_like_collection_room_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 2;</code>
     * @return The likeTotalNum.
     */
    int getLikeTotalNum();

    /**
     * <pre>
     * 今日我已领取的收藏室点赞奖励次数
     * </pre>
     *
     * <code>uint32 claimed_like_reward_num = 3;</code>
     * @return The claimedLikeRewardNum.
     */
    int getClaimedLikeRewardNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c}
   */
  public static final class charm_like_collection_room_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c)
      charm_like_collection_room_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_like_collection_room_s2c.newBuilder() to construct.
    private charm_like_collection_room_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_like_collection_room_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_like_collection_room_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int LIKE_TOTAL_NUM_FIELD_NUMBER = 2;
    private int likeTotalNum_ = 0;
    /**
     * <pre>
     * 收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 2;</code>
     * @return The likeTotalNum.
     */
    @java.lang.Override
    public int getLikeTotalNum() {
      return likeTotalNum_;
    }

    public static final int CLAIMED_LIKE_REWARD_NUM_FIELD_NUMBER = 3;
    private int claimedLikeRewardNum_ = 0;
    /**
     * <pre>
     * 今日我已领取的收藏室点赞奖励次数
     * </pre>
     *
     * <code>uint32 claimed_like_reward_num = 3;</code>
     * @return The claimedLikeRewardNum.
     */
    @java.lang.Override
    public int getClaimedLikeRewardNum() {
      return claimedLikeRewardNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      if (likeTotalNum_ != 0) {
        output.writeUInt32(2, likeTotalNum_);
      }
      if (claimedLikeRewardNum_ != 0) {
        output.writeUInt32(3, claimedLikeRewardNum_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      if (likeTotalNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, likeTotalNum_);
      }
      if (claimedLikeRewardNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, claimedLikeRewardNum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (getLikeTotalNum()
          != other.getLikeTotalNum()) return false;
      if (getClaimedLikeRewardNum()
          != other.getClaimedLikeRewardNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (37 * hash) + LIKE_TOTAL_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getLikeTotalNum();
      hash = (37 * hash) + CLAIMED_LIKE_REWARD_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getClaimedLikeRewardNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        likeTotalNum_ = 0;
        claimedLikeRewardNum_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.likeTotalNum_ = likeTotalNum_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.claimedLikeRewardNum_ = claimedLikeRewardNum_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (other.getLikeTotalNum() != 0) {
          setLikeTotalNum(other.getLikeTotalNum());
        }
        if (other.getClaimedLikeRewardNum() != 0) {
          setClaimedLikeRewardNum(other.getClaimedLikeRewardNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                likeTotalNum_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                claimedLikeRewardNum_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private int likeTotalNum_ ;
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @return The likeTotalNum.
       */
      @java.lang.Override
      public int getLikeTotalNum() {
        return likeTotalNum_;
      }
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @param value The likeTotalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLikeTotalNum(int value) {

        likeTotalNum_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        likeTotalNum_ = 0;
        onChanged();
        return this;
      }

      private int claimedLikeRewardNum_ ;
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 3;</code>
       * @return The claimedLikeRewardNum.
       */
      @java.lang.Override
      public int getClaimedLikeRewardNum() {
        return claimedLikeRewardNum_;
      }
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 3;</code>
       * @param value The claimedLikeRewardNum to set.
       * @return This builder for chaining.
       */
      public Builder setClaimedLikeRewardNum(int value) {

        claimedLikeRewardNum_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日我已领取的收藏室点赞奖励次数
       * </pre>
       *
       * <code>uint32 claimed_like_reward_num = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClaimedLikeRewardNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        claimedLikeRewardNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_like_collection_room_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_like_collection_room_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_like_collection_room_s2c>() {
      @java.lang.Override
      public charm_like_collection_room_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_like_collection_room_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_like_collection_room_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_like_collection_room_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_collection_room_like_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   * 查看收藏室点赞记录
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s}
   */
  public static final class charm_collection_room_like_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s)
      charm_collection_room_like_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_collection_room_like_list_c2s.newBuilder() to construct.
    private charm_collection_room_like_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_collection_room_like_list_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_collection_room_like_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 查看收藏室点赞记录
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_collection_room_like_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_collection_room_like_list_c2s>() {
      @java.lang.Override
      public charm_collection_room_like_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_collection_room_like_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_collection_room_like_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_collection_room_like_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info> 
        getLikeListList();
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_like_info getLikeList(int index);
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    int getLikeListCount();
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder> 
        getLikeListOrBuilderList();
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder getLikeListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c}
   */
  public static final class charm_collection_room_like_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c)
      charm_collection_room_like_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_collection_room_like_list_s2c.newBuilder() to construct.
    private charm_collection_room_like_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_collection_room_like_list_s2c() {
      likeList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_collection_room_like_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.Builder.class);
    }

    public static final int LIKE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info> likeList_;
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info> getLikeListList() {
      return likeList_;
    }
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder> 
        getLikeListOrBuilderList() {
      return likeList_;
    }
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    @java.lang.Override
    public int getLikeListCount() {
      return likeList_.size();
    }
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_like_info getLikeList(int index) {
      return likeList_.get(index);
    }
    /**
     * <pre>
     * 点赞记录列表
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder getLikeListOrBuilder(
        int index) {
      return likeList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < likeList_.size(); i++) {
        output.writeMessage(1, likeList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < likeList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, likeList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c) obj;

      if (!getLikeListList()
          .equals(other.getLikeListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getLikeListCount() > 0) {
        hash = (37 * hash) + LIKE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getLikeListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (likeListBuilder_ == null) {
          likeList_ = java.util.Collections.emptyList();
        } else {
          likeList_ = null;
          likeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c result) {
        if (likeListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            likeList_ = java.util.Collections.unmodifiableList(likeList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.likeList_ = likeList_;
        } else {
          result.likeList_ = likeListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c.getDefaultInstance()) return this;
        if (likeListBuilder_ == null) {
          if (!other.likeList_.isEmpty()) {
            if (likeList_.isEmpty()) {
              likeList_ = other.likeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureLikeListIsMutable();
              likeList_.addAll(other.likeList_);
            }
            onChanged();
          }
        } else {
          if (!other.likeList_.isEmpty()) {
            if (likeListBuilder_.isEmpty()) {
              likeListBuilder_.dispose();
              likeListBuilder_ = null;
              likeList_ = other.likeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              likeListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getLikeListFieldBuilder() : null;
            } else {
              likeListBuilder_.addAllMessages(other.likeList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_charm_like_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_charm_like_info.parser(),
                        extensionRegistry);
                if (likeListBuilder_ == null) {
                  ensureLikeListIsMutable();
                  likeList_.add(m);
                } else {
                  likeListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info> likeList_ =
        java.util.Collections.emptyList();
      private void ensureLikeListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          likeList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_charm_like_info>(likeList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_like_info, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder> likeListBuilder_;

      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info> getLikeListList() {
        if (likeListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(likeList_);
        } else {
          return likeListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public int getLikeListCount() {
        if (likeListBuilder_ == null) {
          return likeList_.size();
        } else {
          return likeListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_like_info getLikeList(int index) {
        if (likeListBuilder_ == null) {
          return likeList_.get(index);
        } else {
          return likeListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder setLikeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_like_info value) {
        if (likeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLikeListIsMutable();
          likeList_.set(index, value);
          onChanged();
        } else {
          likeListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder setLikeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder builderForValue) {
        if (likeListBuilder_ == null) {
          ensureLikeListIsMutable();
          likeList_.set(index, builderForValue.build());
          onChanged();
        } else {
          likeListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder addLikeList(org.gof.demo.worldsrv.msg.Define.p_charm_like_info value) {
        if (likeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLikeListIsMutable();
          likeList_.add(value);
          onChanged();
        } else {
          likeListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder addLikeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_like_info value) {
        if (likeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLikeListIsMutable();
          likeList_.add(index, value);
          onChanged();
        } else {
          likeListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder addLikeList(
          org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder builderForValue) {
        if (likeListBuilder_ == null) {
          ensureLikeListIsMutable();
          likeList_.add(builderForValue.build());
          onChanged();
        } else {
          likeListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder addLikeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder builderForValue) {
        if (likeListBuilder_ == null) {
          ensureLikeListIsMutable();
          likeList_.add(index, builderForValue.build());
          onChanged();
        } else {
          likeListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder addAllLikeList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_charm_like_info> values) {
        if (likeListBuilder_ == null) {
          ensureLikeListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, likeList_);
          onChanged();
        } else {
          likeListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder clearLikeList() {
        if (likeListBuilder_ == null) {
          likeList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          likeListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public Builder removeLikeList(int index) {
        if (likeListBuilder_ == null) {
          ensureLikeListIsMutable();
          likeList_.remove(index);
          onChanged();
        } else {
          likeListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder getLikeListBuilder(
          int index) {
        return getLikeListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder getLikeListOrBuilder(
          int index) {
        if (likeListBuilder_ == null) {
          return likeList_.get(index);  } else {
          return likeListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder> 
           getLikeListOrBuilderList() {
        if (likeListBuilder_ != null) {
          return likeListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(likeList_);
        }
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder addLikeListBuilder() {
        return getLikeListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_charm_like_info.getDefaultInstance());
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder addLikeListBuilder(
          int index) {
        return getLikeListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.getDefaultInstance());
      }
      /**
       * <pre>
       * 点赞记录列表
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_charm_like_info like_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder> 
           getLikeListBuilderList() {
        return getLikeListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_charm_like_info, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder> 
          getLikeListFieldBuilder() {
        if (likeListBuilder_ == null) {
          likeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_charm_like_info, org.gof.demo.worldsrv.msg.Define.p_charm_like_info.Builder, org.gof.demo.worldsrv.msg.Define.p_charm_like_infoOrBuilder>(
                  likeList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          likeList_ = null;
        }
        return likeListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_collection_room_like_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_collection_room_like_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_collection_room_like_list_s2c>() {
      @java.lang.Override
      public charm_collection_room_like_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_collection_room_like_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_collection_room_like_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_collection_room_like_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_target_charm_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * <pre>
   * 查看目标美观值信息
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s}
   */
  public static final class charm_target_charm_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s)
      charm_target_charm_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_target_charm_info_c2s.newBuilder() to construct.
    private charm_target_charm_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_target_charm_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_target_charm_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 查看目标美观值信息
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_target_charm_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_target_charm_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<charm_target_charm_info_c2s>() {
      @java.lang.Override
      public charm_target_charm_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_target_charm_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_target_charm_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface charm_target_charm_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 目标玩家收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 2;</code>
     * @return The likeTotalNum.
     */
    int getLikeTotalNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c}
   */
  public static final class charm_target_charm_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c)
      charm_target_charm_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use charm_target_charm_info_s2c.newBuilder() to construct.
    private charm_target_charm_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private charm_target_charm_info_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new charm_target_charm_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int LIKE_TOTAL_NUM_FIELD_NUMBER = 2;
    private int likeTotalNum_ = 0;
    /**
     * <pre>
     * 目标玩家收藏室的总点赞次数
     * </pre>
     *
     * <code>uint32 like_total_num = 2;</code>
     * @return The likeTotalNum.
     */
    @java.lang.Override
    public int getLikeTotalNum() {
      return likeTotalNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      if (likeTotalNum_ != 0) {
        output.writeUInt32(2, likeTotalNum_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      if (likeTotalNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, likeTotalNum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (getLikeTotalNum()
          != other.getLikeTotalNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (37 * hash) + LIKE_TOTAL_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getLikeTotalNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        likeTotalNum_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.likeTotalNum_ = likeTotalNum_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (other.getLikeTotalNum() != 0) {
          setLikeTotalNum(other.getLikeTotalNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                likeTotalNum_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private int likeTotalNum_ ;
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @return The likeTotalNum.
       */
      @java.lang.Override
      public int getLikeTotalNum() {
        return likeTotalNum_;
      }
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @param value The likeTotalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLikeTotalNum(int value) {

        likeTotalNum_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家收藏室的总点赞次数
       * </pre>
       *
       * <code>uint32 like_total_num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        likeTotalNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.charm_target_charm_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<charm_target_charm_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<charm_target_charm_info_s2c>() {
      @java.lang.Override
      public charm_target_charm_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<charm_target_charm_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<charm_target_charm_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCharm.charm_target_charm_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017msg.charm.proto\022\031org.gof.demo.worldsrv" +
      ".msg\032\roptions.proto\032\014define.proto\"\326\001\n\016ch" +
      "arm_info_s2c\022\023\n\013charm_value\030\001 \001(\r\022\027\n\017sho" +
      "w_medal_list\030\002 \003(\r\022\"\n\032received_level_rew" +
      "ard_list\030\003 \003(\r\022\031\n\021unlock_medal_list\030\004 \003(" +
      "\r\022\026\n\016like_total_num\030\005 \001(\r\022\037\n\027claimed_lik" +
      "e_reward_num\030\006 \001(\r\022\026\n\016today_like_set\030\007 \003" +
      "(\004:\006\210\303\032\314\205\001\"8\n\034charm_claim_level_reward_c" +
      "2s\022\020\n\010charm_sn\030\001 \001(\r:\006\210\303\032\315\205\001\"8\n\034charm_cl" +
      "aim_level_reward_s2c\022\020\n\010charm_sn\030\001 \001(\r:\006" +
      "\210\303\032\315\205\001\"A\n\036charm_save_show_medal_list_c2s" +
      "\022\027\n\017show_medal_list\030\001 \003(\r:\006\210\303\032\316\205\001\"A\n\036cha" +
      "rm_save_show_medal_list_s2c\022\027\n\017show_meda" +
      "l_list\030\001 \003(\r:\006\210\303\032\316\205\001\"S\n\025charm_update_inf" +
      "o_s2c\022\023\n\013charm_value\030\001 \001(\r\022\035\n\025new_unlock" +
      "_medal_list\030\002 \003(\r:\006\210\303\032\317\205\001\";\n\036charm_colle" +
      "ction_room_info_c2s\022\021\n\ttarget_id\030\001 \001(\004:\006" +
      "\210\303\032\320\205\001\"\355\002\n\036charm_collection_room_info_s2" +
      "c\022\021\n\ttarget_id\030\001 \001(\004\022\014\n\004name\030\002 \001(\t\022/\n\004he" +
      "ad\030\003 \001(\0132!.org.gof.demo.worldsrv.msg.p_h" +
      "ead\022\r\n\005level\030\004 \001(\r\022\020\n\010charm_sn\030\005 \001(\r\022\027\n\017" +
      "show_medal_list\030\006 \003(\r\022\026\n\016like_total_num\030" +
      "\007 \001(\r\022D\n\016role_area_list\030\010 \003(\0132,.org.gof." +
      "demo.worldsrv.msg.p_charm_role_area\022F\n\no" +
      "ther_area\030\t \003(\01322.org.gof.demo.worldsrv." +
      "msg.p_charm_other_area_item\022\021\n\tserver_id" +
      "\030\n \001(\r:\006\210\303\032\320\205\001\"\263\001\n\033charm_save_display_ar" +
      "ea_c2s\022D\n\016role_area_list\030\001 \003(\0132,.org.gof" +
      ".demo.worldsrv.msg.p_charm_role_area\022F\n\n" +
      "other_area\030\002 \003(\01322.org.gof.demo.worldsrv" +
      ".msg.p_charm_other_area_item:\006\210\303\032\321\205\001\"\263\001\n" +
      "\033charm_save_display_area_s2c\022D\n\016role_are" +
      "a_list\030\001 \003(\0132,.org.gof.demo.worldsrv.msg" +
      ".p_charm_role_area\022F\n\nother_area\030\002 \003(\01322" +
      ".org.gof.demo.worldsrv.msg.p_charm_other" +
      "_area_item:\006\210\303\032\321\205\001\";\n\036charm_like_collect" +
      "ion_room_c2s\022\021\n\ttarget_id\030\001 \001(\004:\006\210\303\032\322\205\001\"" +
      "t\n\036charm_like_collection_room_s2c\022\021\n\ttar" +
      "get_id\030\001 \001(\004\022\026\n\016like_total_num\030\002 \001(\r\022\037\n\027" +
      "claimed_like_reward_num\030\003 \001(\r:\006\210\303\032\322\205\001\"-\n" +
      "#charm_collection_room_like_list_c2s:\006\210\303" +
      "\032\323\205\001\"n\n#charm_collection_room_like_list_" +
      "s2c\022?\n\tlike_list\030\001 \003(\0132,.org.gof.demo.wo" +
      "rldsrv.msg.p_charm_like_info:\006\210\303\032\323\205\001\"8\n\033" +
      "charm_target_charm_info_c2s\022\021\n\ttarget_id" +
      "\030\001 \001(\004:\006\210\303\032\324\205\001\"P\n\033charm_target_charm_inf" +
      "o_s2c\022\021\n\ttarget_id\030\001 \001(\004\022\026\n\016like_total_n" +
      "um\030\002 \001(\r:\006\210\303\032\324\205\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_info_s2c_descriptor,
        new java.lang.String[] { "CharmValue", "ShowMedalList", "ReceivedLevelRewardList", "UnlockMedalList", "LikeTotalNum", "ClaimedLikeRewardNum", "TodayLikeSet", });
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_c2s_descriptor,
        new java.lang.String[] { "CharmSn", });
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_claim_level_reward_s2c_descriptor,
        new java.lang.String[] { "CharmSn", });
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_c2s_descriptor,
        new java.lang.String[] { "ShowMedalList", });
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_save_show_medal_list_s2c_descriptor,
        new java.lang.String[] { "ShowMedalList", });
    internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_update_info_s2c_descriptor,
        new java.lang.String[] { "CharmValue", "NewUnlockMedalList", });
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_c2s_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_info_s2c_descriptor,
        new java.lang.String[] { "TargetId", "Name", "Head", "Level", "CharmSn", "ShowMedalList", "LikeTotalNum", "RoleAreaList", "OtherArea", "ServerId", });
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_c2s_descriptor,
        new java.lang.String[] { "RoleAreaList", "OtherArea", });
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_save_display_area_s2c_descriptor,
        new java.lang.String[] { "RoleAreaList", "OtherArea", });
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_c2s_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_like_collection_room_s2c_descriptor,
        new java.lang.String[] { "TargetId", "LikeTotalNum", "ClaimedLikeRewardNum", });
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_collection_room_like_list_s2c_descriptor,
        new java.lang.String[] { "LikeList", });
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_c2s_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_charm_target_charm_info_s2c_descriptor,
        new java.lang.String[] { "TargetId", "LikeTotalNum", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
