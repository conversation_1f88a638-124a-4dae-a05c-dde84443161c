<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="src" path="seam"/>
	<classpathentry kind="src" path="gen"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="lib" path="../libs/commons-beanutils-1.8.3.jar"/>
	<classpathentry kind="lib" path="../libs/commons-beanutils-bean-collections-1.8.3.jar"/>
	<classpathentry kind="lib" path="../libs/commons-beanutils-core-1.8.3.jar"/>
	<classpathentry kind="lib" path="../libs/commons-collections-3.2.1.jar"/>
	<classpathentry kind="lib" path="../libs/commons-collections4-4.4.jar"/>
	<classpathentry kind="lib" path="../libs/commons-lang3.jar"/>
	<classpathentry kind="lib" path="../libs/commons-logging-1.1.3.jar"/>
	<classpathentry kind="lib" path="../libs/commons-logging-api-1.1.3.jar"/>
	<classpathentry kind="lib" path="../libs/dom4j.jar"/>
	<classpathentry kind="lib" path="../libs/fastjson-1.2.75.jar"/>
	<classpathentry kind="lib" path="../libs/freemarker.jar"/>
	<classpathentry kind="lib" path="../libs/httpclient.jar"/>
	<classpathentry kind="lib" path="../libs/httpcore.jar"/>
	<classpathentry kind="lib" path="../libs/javassist.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-api.jar" sourcepath="C:/Users/<USER>/AppData/Local/Temp/.org.sf.feeling.decompiler1547612820550/source/log4j-api-2.0-beta9-sources.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-core.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-slf4j-impl.jar"/>
	<classpathentry kind="lib" path="../libs/mysql-connector-java-5.1.48-bin.jar"/>
	<classpathentry kind="lib" path="../libs/netty-all-4.1.89.Final.jar"/>
	<classpathentry kind="lib" path="../libs/quartz.jar"/>
	<classpathentry kind="lib" path="../libs/quartz-jobs.jar"/>
	<classpathentry kind="lib" path="../libs/slf4j-api.jar"/>
	<classpathentry kind="lib" path="../libs/zmq.jar"/>
	<classpathentry kind="lib" path="../libs/protobuf.jar"/>
	<classpathentry kind="lib" path="../libs/logOp-1.8.jar" sourcepath="/operation-log/src"/>
	<classpathentry kind="lib" path="../libs/classReloader.jar"/>
	<classpathentry kind="lib" path="../config"/>
	<classpathentry kind="lib" path="../plant/json"/>
	<classpathentry kind="lib" path="../plant/nameFix"/>
	<classpathentry kind="lib" path="../plant/stageConfig"/>
	<classpathentry combineaccessrules="false" kind="src" path="/gof"/>
	<classpathentry combineaccessrules="false" kind="src" path="/platform"/>
	<classpathentry kind="con" path="org.eclipse.jdt.junit.JUNIT_CONTAINER/5"/>
	<classpathentry kind="lib" path="../libs/jts-1.13.jar"/>
	<classpathentry kind="lib" path="../libs/guava-19.0.jar"/>
	<classpathentry kind="lib" path="../libs/cron-utils-9.1.1.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
