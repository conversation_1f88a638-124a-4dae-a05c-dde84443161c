package test.org.gof.test;

import io.vertx.core.AbstractVerticle;
import org.apache.logging.log4j.LogManager;
import org.gof.demo.seam.main.WorldStartup;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class TestWorldBase extends AbstractVerticle {

    public static Logger logger ;

    @BeforeAll
    public static void startTestEvn() {
//        System.setProperty("logFileName", "game-test-log");
//        System.setProperty("vertx.logger-delegate-factory-class-name","io.vertx.core.logging.Log4j2LogDelegateFactory");
        try {
            String[] args = new String[0];
            WorldStartup.main(args);
            logger = LoggerFactory.getLogger(TestWorldBase.class);
            Thread.sleep(3000);
        }catch (Exception ex){
            logger.error("初始化测试环境失败,ex="+ex, ex);
            System.exit(1);
        }
    }

    @AfterAll
    public void stopTestEvn(){
//        String[] args = new String[0];
//        WorldStartup.main(args);
        LogManager.shutdown();
    }

}