package test.org.gof.redis;

import io.vertx.core.Future;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.support.Distr;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.msg.Define;
import test.org.gof.test.TestWorldBase;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class EntityManagerTest extends TestWorldBase {


    @Test
    public void testRedisSetEntities() throws InterruptedException {
        AwaitUtil.awaitResult(handler->{
            Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
            port.doAction(()-> {
                List<HumanBrief> briefList = mockList();
                EntityManager.redisSetEntities(briefList);
                handler.handle(Future.succeededFuture(true));
            });
        });
        System.out.println("test finish");
        Thread.sleep(5000);
    }

    private List<HumanBrief>  mockList(){
        List<HumanBrief> list = new ArrayList<>();
        for(int n=0;n<10;n++) {
            HumanBrief humanBrief = new HumanBrief();
            humanBrief.setId(12320L+n);
            humanBrief.setName("test1232_"+n);
            humanBrief.setLevel(12);
            Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
            dInfo.setId(humanBrief.getId());
            dInfo.setName(humanBrief.getName());
            dInfo.setLev(humanBrief.getLevel());
            dInfo.setJob(1);
            for (int i = 0; i < 10; i++) {
                dInfo.addPetList(Define.p_role_pet.newBuilder().setPetId(1 + i).setPetLev(10).setPetPos(7).build());
            }
            for (int i = 0; i < 10; i++) {
                dInfo.addAttrObjList(Define.p_attr_obj_list.newBuilder().addAttrList(Define.p_key_value.newBuilder().setK(10 + i).setV(10000 + i).build()).build());
            }
            humanBrief.setBattleRole(dInfo.build().toByteArray());
            list.add(humanBrief);
        }
        return list;
    }


    @Test
    public void testHumanBrief(){
        CompletableFuture<List<HumanBrief>> future = new CompletableFuture<>();

        List<HumanBrief> humanBriefs = AwaitUtil.awaitResult(hander->{
            Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
            port.doAction(()-> {
                List<Long> ids = new ArrayList<>(Arrays.asList(new Long[]{600060000160800001L,
                        600070000008400001L}));

                EntityManager.batchGetEntity(HumanBrief.class, ids, res -> {
                    hander.handle(Future.succeededFuture(res.result()));
                });
            });
        },120000);
        System.out.println(humanBriefs.size());


    }

    @Test
    public void testGetEntityBatch() throws ExecutionException, InterruptedException {
        CompletableFuture<List<Human>> future = new CompletableFuture<>();
        Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
        port.doAction(()->{
            List<Long> ids = new ArrayList<>(Arrays.asList(new Long[]{600010000000000076L,
                    600010000000000145L,
                    600010000000000179L,
                    600010000000000218L,
                    600010000000000246L,
                    600010000000000260L,}));

            EntityManager.batchGetEntity(Human.class, ids, res->{
                if(res.failed()){
                    logger.error("getEntityBatch failed !",res.cause());
                    future.completeExceptionally(res.cause());
                    return;
                }
                List<Human> humanList = res.result();
                future.complete(humanList);

            });
        });
        List<Human> humanList = future.get();
        humanList.forEach(human->{
            logger.info("id={}, account={}, name={}",human.getId(),human.getAccount(),human.getName());
        });
    }
}
